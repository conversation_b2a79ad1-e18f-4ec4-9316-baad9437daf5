<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="AsyncWebDriver" Version="1.4.6"/>
        <PackageReference Include="Dapper" Version="2.1.35"/>
        <PackageReference Include="HtmlAgilityPack" Version="1.11.65"/>
        <PackageReference Include="Npgsql" Version="8.0.3"/>
        <PackageReference Include="RestSharp" Version="112.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.2"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="8.0.1"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0"/>
        <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0"/>
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
        <PackageReference Include="Selenium.WebDriver" Version="4.24.0"/>
        <PackageReference Include="Serilog" Version="4.0.1"/>
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.2"/>
        <PackageReference Include="Serilog.Expressions" Version="5.0.0"/>
        <PackageReference Include="Serilog.Formatting.Compact" Version="3.0.0"/>
        <PackageReference Include="Serilog.Sinks.Async" Version="2.0.0"/>
        <PackageReference Include="Serilog.Sinks.Confluent.Kafka" Version="1.10.0"/>
        <PackageReference Include="Serilog.Sinks.File" Version="6.0.0"/>
        <PackageReference Include="Serilog.Sinks.PeriodicBatching" Version="5.0.0"/>
        <PackageReference Include="System.Drawing.Common" Version="8.0.8"/>
    </ItemGroup>
    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.Development.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>
    <ItemGroup>
      <Folder Include="Models\LuatDauThau\ThongTinDieuChinh\" />
    </ItemGroup>
</Project>
