using Newtonsoft.Json;
using RestSharp;
using Serilog;
using VNEPS_PROJECT_OLD.Models.LuaChonNhaDauTu;
using VNEPS_PROJECT_OLD.Models.Payload;
using VNEPS_PROJECT_OLD.Models.VonKhac;
using DetailDTO = VNEPS_PROJECT_OLD.Models.LuaChonNhaDauTu.DetailDTO;

namespace VNEPS_PROJECT_OLD.LuaChonNhaDauTu;

public class Ket_Qua_Danh_Gia_So_Bo_Nang_Luc
{
    private const string UrlOrigin = "https://muasamcong.mpi.gov.vn/api/unau/smartsearchold/es/search";
    private readonly IRestClient _restClient = new RestClient();
    private readonly int _maxRecords = 10000;
    private const string OutputFolder = "Data/LuaChonNhaDauTu/KetQuaDanhGiaSoBoNangLuc";
    private readonly SemaphoreSlim _rateLimitSemaphore = new(5, 5); // Limit concurrent requests
    private const int RequestDelayMs = 1000;

    public Ket_Qua_Danh_Gia_So_Bo_Nang_Luc()
    {
        SetupLogging();
        Log.Information("Starting data processing...");
    }

    private async Task<T> ExecuteWithRateLimitAsync<T>(Func<Task<T>> apiCall)
    {
        await _rateLimitSemaphore.WaitAsync();
        try
        {
            Log.Information("Executing API call... {ApiCall}", apiCall.Method.Name);
            await Task.Delay(RequestDelayMs); // Add delay between requests
            return await apiCall();
        }
        finally
        {
            _rateLimitSemaphore.Release();
        }
    }

    private static void SetupLogging()
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .CreateLogger();
    }

    public async Task ProcessAsync()
    {
        Log.Information($"Processing data ");
        await FetchAndProcessDataForRange();
    }

    private static Task CheckFolderAsync(string? path)
    {
        return Task.Run(() =>
        {
            if (Directory.Exists(path)) return;
            Log.Information($"Creating folder {path}");
            Directory.CreateDirectory(path ?? throw new ArgumentNullException(nameof(path)));
        });
    }

    private async Task FetchAndProcessDataForRange()
    {
        var listObjectSave = new List<object>();
        var semaphore = new SemaphoreSlim(10); // Limit to 10 concurrent tasks

        try
        {
            var count = await ExecuteWithRateLimitAsync(() => GetTotalRecords());
            Log.Information($"Total records: {count}");
            if (count == 0)
            {
                Log.Information($"No data found");
                return;
            }

            var countTotalPage = count / _maxRecords + 1;
            Log.Information($"Total pages: {countTotalPage}");

            for (var i = 0; i < countTotalPage; i++)
            {
                Log.Information($"Processing page {i + 1} of {countTotalPage}");
                var lstResult = await ExecuteWithRateLimitAsync(() => Search(i, _maxRecords));
                if (lstResult.Count == 0)
                {
                    Log.Information("No data found");
                    return;
                }

                Log.Information($"Processing data for {lstResult.Count} records");

                var tasks = lstResult.Select(async searchReSearch =>
                {
                    await semaphore.WaitAsync();
                    try
                    {
                        Log.Information($"Processing data for {searchReSearch.Code} - {searchReSearch.Version}");
                        var versionToFetch = GetVersionsToFetch(searchReSearch.Version!);

                        var dataVersion = await Task.WhenAll(versionToFetch.Select(async version =>
                            await FetchDetailedDataForVersion(searchReSearch.Code!, version)));

                        return new
                        {
                            thongTinTuDanhSachTimKiem = searchReSearch,
                            dataVersion
                        };
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }).ToList();

                var processedResults = await Task.WhenAll(tasks);
                listObjectSave.AddRange(processedResults);
            }
        }
        catch (Exception e)
        {
            Log.Error(e, "Error processing data");
        }

        Log.Information($"Saving data ");
        await SaveDataToFileAsync(listObjectSave);
    }

    private async Task<object> FetchDetailedDataForVersion(string bidCode, string version)
    {
        var winningBidInfo = await ExecuteWithRateLimitAsync(async () =>
        {
            Log.Information($"Fetching data for {bidCode} - {version}");
            var detail = await GetDetail(bidCode, version);
            return new
            {
                detail,
            };
        });

        return new
        {
            winningBidInfo
        };
    }

    private RestRequest CreateRestRequest(string url, int pageNumber = 0,
        int pageSize = 10000)
    {
        var restRequest = new RestRequest(url)
        {
            Method = Method.Post,
            RequestFormat = DataFormat.Json,
            Timeout = new TimeSpan(0, 0, 30),
        };

        var payLoad = new BodyPayloadCommon
        {
            Body =
            [
                new Body
                {
                    PageSize = pageSize,
                    PageNumber = pageNumber,
                    Query =
                    [
                        new Query
                        {
                            Index = "es-ebid-search",
                            KeyWord = "",
                            MatchType = "all-1",
                            MatchFields =
                                ["code", "name"],
                            Filters =
                            [
                                new Filter
                                {
                                    FieldName = "type", SearchType = "in", FieldValues = ["kqdgsbnlkn_sdd"]
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        restRequest.AddJsonBody(payLoad);
        return restRequest;
    }

    private async Task<int> GetTotalRecords(int pageNumber = 0,
        int pageSize = 10)
    {
        var restRequest = CreateRestRequest(UrlOrigin, pageNumber, pageSize);
        try
        {
            Log.Information("Getting total records.. in {fromTime:yyyy-MM-dd} - {toTime:yyyy-MM-dd}");
            var response = await _restClient.ExecuteAsync<ResponseModel<ContentVonKhacSearch>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data!.Body?.Page?.TotalElements ?? 0;
            }

            Log.Warning($"Warning getting total records: {response.ErrorMessage}");
            return 0;
        }
        catch (Exception e)
        {
            Log.Error($"Error getting total records: {e.Message}");
            return 0;
        }
    }

    private async Task<List<ContentKetQuaDanhGiaSoBo>> Search(int pageNumber = 0, int pageSize = 10000)
    {
        var restRequest = CreateRestRequest(UrlOrigin, pageNumber, pageSize);
        try
        {
            Log.Information("Searching data.. in {fromTime:yyyy-MM-dd} - {toTime:yyyy-MM-dd}");
            var response =
                await _restClient.ExecuteAsync<ResponseModel<ContentKetQuaDanhGiaSoBo>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data!.Body?.Page?.Content ?? [];
            }

            Log.Warning($"Warning searching data: {response.ErrorMessage}");

            return new();
        }
        catch (Exception e)
        {
            Log.Error($"Error searching data: {e.Message}");
            return new();
        }
    }

    private async Task<DetailKetQuaDanhGiaSoBo> GetDetail(string bidNo, string bidTurnNo)
    {
        Log.Information($"Get detail for {bidNo} - {bidTurnNo}");
        var restRequest =
            new RestRequest(
                "https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/lcndt/resultNLKN-detail")
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

        //{"body":{"bidNo":"20220319513","bidTurnNo":"00","userName":null}}
        var payLoad = new
        {
            body = new
            {
                bidNo = bidNo,
                bidTurnNo = $"{bidTurnNo}"
            }
        };

        restRequest.AddJsonBody(payLoad);
        try
        {
            var response =
                await _restClient.ExecuteAsync(restRequest);
            if (response is { IsSuccessful: true })
            {
                var data =
                    JsonConvert.DeserializeObject<ApiResponse<DetailKetQuaDanhGiaSoBo>>(response.Content);
                return data.Body;
            }

            Log.Warning($"Warning searching data: {response.ErrorMessage}");

            return new();
        }
        catch (Exception e)
        {
            Log.Error($"Error searching data: {e.Message}");
            return new();
        }
    }

    private List<string> GetVersionsToFetch(string currentVersion)
    {
        Log.Information($"Getting versions to fetch for {currentVersion}");
        var versions = new List<string> { currentVersion };
        int versionNumber = int.Parse(currentVersion);

        while (versionNumber > 0)
        {
            Log.Information($"Adding version {versionNumber}");
            versionNumber--;
            versions.Add(versionNumber.ToString("D2"));
        }

        return versions;
    }

    private Task WriteJsonToFileAsync(StreamWriter streamWriter, string json)
    {
        Log.Information($"Writing data to file");
        return streamWriter.WriteAsync(json);
    }


    private async Task SaveDataToFileAsync(List<object> contents)
    {
        var path = Path.Combine(Directory.GetCurrentDirectory(), OutputFolder);
        await CheckFolderAsync(path);

        var fileName = $"{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.json";
        var fullPath = Path.Combine(path, fileName);

        try
        {
            Log.Information($"Saving data to file {fullPath}");
            await using var streamWriter = new StreamWriter(fullPath);
            var json = JsonConvert.SerializeObject(contents, Formatting.Indented);
            await WriteJsonToFileAsync(streamWriter, json);
        }
        catch (Exception e)
        {
            Log.Error(e, "Error saving data to file");
        }
    }
}