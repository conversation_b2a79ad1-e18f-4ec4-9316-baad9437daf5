using Newtonsoft.Json;
using RestSharp;
using Serilog;
using VNEPS_PROJECT_OLD.Models.Payload;
using VNEPS_PROJECT_OLD.Models.VonKhac;

namespace VNEPS_PROJECT_OLD.QuyTrinhApDungVonKhac;

public class Ket_Hoach_Lua_Chon_Nha_Thau
{
    private const string UrlOrigin = "https://muasamcong.mpi.gov.vn/api/unau/smartsearchold/es/search";
    private readonly IRestClient _restClient = new RestClient();
    private readonly int _maxRecords = 10000;
    private const string OutputFolder = "Data/QuyTrinhApDungVonKhac/KeHoachLuaChonNhaThau";
    private readonly SemaphoreSlim _rateLimitSemaphore = new(5, 5); // Limit concurrent requests
    private const int RequestDelayMs = 1000;

    private async Task<T> ExecuteWithRateLimitAsync<T>(Func<Task<T>> apiCall)
    {
        await _rateLimitSemaphore.WaitAsync();
        try
        {
            Log.Information("Executing API call... {ApiCall}", apiCall.Method.Name);
            await Task.Delay(RequestDelayMs); // Add delay between requests
            return await apiCall();
        }
        finally
        {
            _rateLimitSemaphore.Release();
        }
    }

    public Ket_Hoach_Lua_Chon_Nha_Thau()
    {
        SetupLogging();
        Log.Information("Starting data processing...");
    }

    private static void SetupLogging()
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .CreateLogger();
    }


    public async Task ProcessAsync()
    {
        Log.Information($"Processing data ");
        await FetchAndProcessDataForRange();
    }

    private static Task CheckFolderAsync(string? path)
    {
        return Task.Run(() =>
        {
            if (Directory.Exists(path)) return;
            Log.Information($"Creating folder {path}");
            Directory.CreateDirectory(path ?? throw new ArgumentNullException(nameof(path)));
        });
    }


    private async Task FetchAndProcessDataForRange()
    {
        var listObjectSave = new List<object>();
        var semaphore = new SemaphoreSlim(10); // Limit to 10 concurrent tasks

        try
        {
            var count = await ExecuteWithRateLimitAsync(() => GetTotalRecords());
            Log.Information($"Total records: {count}");
            if (count == 0)
            {
                Log.Information($"No data found");
                return;
            }

            var countTotalPage = count / _maxRecords + 1;
            Log.Information($"Total pages: {countTotalPage}");

            for (var i = 0; i < countTotalPage; i++)
            {
                Log.Information($"Processing page {i + 1} of {countTotalPage}");
                var lstResult = await ExecuteWithRateLimitAsync(() => Search(i, _maxRecords));
                if (lstResult.Count == 0)
                {
                    Log.Information("No data found");
                    return;
                }

                Log.Information($"Processing data for {lstResult.Count} records");

                var tasks = lstResult.Select(async searchReSearch =>
                {
                    await semaphore.WaitAsync();
                    try
                    {
                        Log.Information($"Processing data for {searchReSearch.Code} - {searchReSearch.Version}");
                        var versionToFetch = GetVersionsToFetch(searchReSearch.Version!);

                        var dataVersion = await Task.WhenAll(versionToFetch.Select(async version =>
                            await FetchDetailedDataForVersion(searchReSearch.BidPlanProjectId!, version)));

                        return new
                        {
                            thongTinTuDanhSachTimKiem = searchReSearch,
                            dataVersion
                        };
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }).ToList();

                var processedResults = await Task.WhenAll(tasks);
                listObjectSave.AddRange(processedResults);
            }
        }
        catch (Exception e)
        {
            Log.Error(e, "Error processing data");
        }

        Log.Information($"Saving data ");
        await SaveDataToFileAsync(listObjectSave);
    }

    private async Task<object> FetchDetailedDataForVersion(string bidCode, string version)
    {
        var winningBidInfo = await ExecuteWithRateLimitAsync(async () =>
        {
            Log.Information($"Fetching data for {bidCode} - {version}");
            var detail = await GetDetail(bidCode, version);
            var listBidPlanBidVkInfo = await GetList(bidCode);
            return new
            {
                detail,
                listBidPlanBidVkInfo
            };
        });

        return new
        {
            winningBidInfo
        };
    }

    private RestRequest CreateRestRequest(string url, int pageNumber = 0,
        int pageSize = 10000)
    {
        var restRequest = new RestRequest(url)
        {
            Method = Method.Post,
            RequestFormat = DataFormat.Json,
            Timeout = new TimeSpan(0, 0, 30),
        };

        var payLoad = new BodyPayloadCommon
        {
            Body =
            [
                new Body
                {
                    PageSize = pageSize,
                    PageNumber = pageNumber,
                    Query =
                    [
                        new Query
                        {
                            Index = "es-ebid-search",
                            KeyWord = "",
                            MatchType = "all-1",
                            MatchFields = ["code", "name", "ten_chu_dau_tu"],
                            Filters =
                            [
                                new Filter
                                {
                                    FieldName = "type", SearchType = "in", FieldValues = ["vk_khlcnt"]
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        restRequest.AddJsonBody(payLoad);
        return restRequest;
    }

    private async Task<int> GetTotalRecords(int pageNumber = 0,
        int pageSize = 10)
    {
        var restRequest = CreateRestRequest(UrlOrigin, pageNumber, pageSize);
        try
        {
            Log.Information("Getting total records.. in {fromTime:yyyy-MM-dd} - {toTime:yyyy-MM-dd}");
            var response = await _restClient.ExecuteAsync<ResponseModel<ContentVonKhacSearch>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data!.Body?.Page?.TotalElements ?? 0;
            }

            Log.Warning($"Warning getting total records: {response.ErrorMessage}");
            return 0;
        }
        catch (Exception e)
        {
            Log.Error($"Error getting total records: {e.Message}");
            return 0;
        }
    }

    private async Task<List<ContentVonKhacSearch>> Search(int pageNumber = 0, int pageSize = 10000)
    {
        var restRequest = CreateRestRequest(UrlOrigin, pageNumber, pageSize);
        try
        {
            Log.Information("Searching data.. in {fromTime:yyyy-MM-dd} - {toTime:yyyy-MM-dd}");
            var response =
                await _restClient.ExecuteAsync<ResponseModel<ContentVonKhacSearch>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data!.Body?.Page?.Content ?? [];
            }

            Log.Warning($"Warning searching data: {response.ErrorMessage}");

            return new List<ContentVonKhacSearch>();
        }
        catch (Exception e)
        {
            Log.Error($"Error searching data: {e.Message}");
            return new List<ContentVonKhacSearch>();
        }
    }

    private async Task<BidPlanProjectVKOfDetailDTO> GetDetail(string bidPlanProjectId, string bidTurnNo)
    {
        Log.Information($"Get detail for {bidPlanProjectId} - {bidTurnNo}");
        var restRequest =
            new RestRequest(
                "https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bid-plan-project-vk/get-detail")
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

        var payLoad = new
        {
            body = new
            {
                bidPlanProjectId = bidPlanProjectId
            }
        };

        restRequest.AddJsonBody(payLoad);
        try
        {
            var response =
                await _restClient.ExecuteAsync(restRequest);
            if (response.IsSuccessful)
            {
                var data = JsonConvert.DeserializeObject<ApiResponse<ContentBidPlanProjectVKOfDetailDTO>>(response.Content);
                return data.Body.bidPlanProjectVKOfDetailDTO;
            }

            Log.Warning($"Warning searching data: {response.ErrorMessage}");

            return new BidPlanProjectVKOfDetailDTO();
        }
        catch (Exception e)
        {
            Log.Error($"Error searching data: {e.Message}");
            return new BidPlanProjectVKOfDetailDTO();
        }
    }

    //https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bid-plan-bid-vk/get-list
    // {"body":{"bidPlanMasterId":"1106097","pageNumber":0,"pageSize":10}}
    private async Task<List<BidPlanBidVkInfoDTO>> GetList(string bidPlanMasterId)
    {
        Log.Information($"Get detail for {bidPlanMasterId}");
        var isContinue = true;
        int pageNumber = 0;
        var result = new List<BidPlanBidVkInfoDTO>();
        const int pageSize = 10;

        while (isContinue)
        {
            var restRequest =
                new RestRequest("https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bid-plan-bid-vk/get-list")
                {
                    Method = Method.Post,
                    RequestFormat = DataFormat.Json,
                    Timeout = new TimeSpan(0, 0, 30),
                };

            var payLoad = new
            {
                body = new
                {
                    bidPlanMasterId = bidPlanMasterId,
                    pageNumber = pageNumber,
                    pageSize = pageSize
                }
            };

            restRequest.AddJsonBody(payLoad);
            try
            {
                var response = await _restClient.ExecuteAsync(restRequest);
                if (response is { IsSuccessful: true })
                {
                    var data = JsonConvert.DeserializeObject<ApiResponse<BidPlanBidVkInfoDTOList>>(response.Content);
                    if (data != null)
                    {
                        if (data.Body.bidPlanBidVkInfoDTOList.Count == 0)
                        {
                            isContinue = false;
                        }
                        else
                        {
                            result.AddRange(data.Body.bidPlanBidVkInfoDTOList);
                            if (data.Body.bidPlanBidVkInfoDTOList.Count < pageSize)
                            {
                                isContinue = false;
                            }
                            else
                            {
                                pageNumber++;
                            }
                        }
                    }
                    else
                    {
                        isContinue = false;
                    }
                }
                else
                {
                    isContinue = false;
                    Log.Warning($"Warning searching data");
                }
            }
            catch (Exception e)
            {
                isContinue = false;
                Log.Error($"Error searching data: {e.Message}");
            }
        }

        return result;
    }


    private List<string> GetVersionsToFetch(string currentVersion)
    {
        Log.Information($"Getting versions to fetch for {currentVersion}");
        var versions = new List<string> { currentVersion };
        int versionNumber = int.Parse(currentVersion);

        while (versionNumber > 0)
        {
            Log.Information($"Adding version {versionNumber}");
            versionNumber--;
            versions.Add(versionNumber.ToString("D2"));
        }

        return versions;
    }

    private Task WriteJsonToFileAsync(StreamWriter streamWriter, string json)
    {
        Log.Information($"Writing data to file");
        return streamWriter.WriteAsync(json);
    }


    private async Task SaveDataToFileAsync(List<object> contents)
    {
        var path = Path.Combine(Directory.GetCurrentDirectory(), OutputFolder);
        await CheckFolderAsync(path);

        var fileName = $"quytrinhapdungvonkhac_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.json";
        var fullPath = Path.Combine(path, fileName);

        try
        {
            Log.Information($"Saving data to file {fullPath}");
            await using var streamWriter = new StreamWriter(fullPath);
            var json = JsonConvert.SerializeObject(contents, Formatting.Indented);
            await WriteJsonToFileAsync(streamWriter, json);
        }
        catch (Exception e)
        {
            Log.Error(e, "Error saving data to file");
        }
    }
}