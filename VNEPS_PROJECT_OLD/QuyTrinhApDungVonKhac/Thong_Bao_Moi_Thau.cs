using Newtonsoft.Json;
using RestSharp;
using Serilog;
using VNEPS_PROJECT_OLD.Models.LuatDauThau.ThongBaoMoiThau;
using VNEPS_PROJECT_OLD.Models.Payload;
using VNEPS_PROJECT_OLD.Models.VonKhac;

namespace VNEPS_PROJECT_OLD.QuyTrinhApDungVonKhac;

public class Thong_Bao_Moi_Thau
{
    private const string UrlOrigin = "https://muasamcong.mpi.gov.vn/api/unau/smartsearchold/es/search";
    private readonly IRestClient _restClient = new RestClient();
    private readonly int _maxRecords = 10000;
    private const string OutputFolder = "Data/QuyTrinhApDungVonKhac/ThongBaoMoiThau";
    private readonly SemaphoreSlim _rateLimitSemaphore = new(5, 5); // Limit concurrent requests
    private const int RequestDelayMs = 1000;
    private DateTime _firstTimeGet;

    private async Task<T> ExecuteWithRateLimitAsync<T>(Func<Task<T>> apiCall)
    {
        await _rateLimitSemaphore.WaitAsync();
        try
        {
            await Task.Delay(RequestDelayMs); // Add delay between requests
            return await apiCall();
        }
        finally
        {
            _rateLimitSemaphore.Release();
        }
    }


    //Constructor

    public Thong_Bao_Moi_Thau()
    {
        _firstTimeGet = GetLatestProcessedDate() != null ? GetLatestProcessedDate()!.Value : new DateTime(2015, 05, 01);
        SetupLogging();
        Log.Information("Starting data processing...");
    }


    public async Task ProcessAsync()
    {
        while (_firstTimeGet <= DateTime.Now)
        {
            Log.Information($"Processing data for {_firstTimeGet:yyyy-MM-dd}");
            await FetchAndProcessDataForRange(_firstTimeGet, _firstTimeGet.AddDays(1).AddSeconds(-1));
            Log.Information($"Finished processing data for {_firstTimeGet:yyyy-MM-dd}");
            _firstTimeGet = _firstTimeGet.AddDays(1);
        }
    }

    private async Task FetchAndProcessDataForRange(DateTime fromTime, DateTime toTime)
    {
        var listObjectSave = new List<object>();

        try
        {
            var count = await ExecuteWithRateLimitAsync(() => GetTotalRecords(fromTime, toTime));
            Log.Information($"Total records: {count}");
            if (count == 0)
            {
                Log.Information($"No data found in day: {fromTime:yyyy-MM-dd} - {toTime:yyyy-MM-dd}");
                return;
            }

            var countTotalPage = count / _maxRecords + 1;
            Log.Information($"Total pages: {countTotalPage}");

            for (var i = 0; i < countTotalPage; i++)
            {
                Log.Information(
                    $"Processing page {i + 1} of {countTotalPage} of data for {fromTime:yyyy-MM-dd} of {toTime:yyyy-MM-dd}");
                var lstResult = await ExecuteWithRateLimitAsync(() => Search(fromTime, toTime, i, _maxRecords));
                if (lstResult.Count == 0)
                {
                    Log.Information("No data found");
                    return;
                }

                // Parallel processing of search results
                var processedResults = await Task.WhenAll(lstResult.Select(async searchReSearch =>
                {
                    Log.Information($"Processing data for {searchReSearch.Code} - {searchReSearch.Version}");
                    var versionToFetch = GetVersionsToFetch(searchReSearch.Version!);

                    var dataVersion = await Task.WhenAll(versionToFetch.Select(async version =>
                        await FetchDetailedDataForVersion(searchReSearch.Code!, version, searchReSearch.BidType!)));

                    return new
                    {
                        thongTinTuDanhSachTimKiem = searchReSearch,
                        dataVersion
                    };
                }));

                listObjectSave.AddRange(processedResults);
            }
        }
        catch (Exception e)
        {
            Log.Error(e, "Error processing data");
        }

        Log.Information($"Saving data for {fromTime:yyyy-MM-dd HH:mm:ss} to {toTime:yyyy-MM-dd HH:mm:ss}");
        await SaveDataToFileAsync(listObjectSave, fromTime, toTime);
    }

    private async Task<object> FetchDetailedDataForVersion(string bidCode, string version, string bidType)
    {
        var notifyYclr = await ExecuteWithRateLimitAsync(() => GetNotifyYclr(bidCode, version));
        var notifyMeeting = await ExecuteWithRateLimitAsync(() => GetNotifyMeeting(bidCode, version));
        var notifyInfoCancel = await ExecuteWithRateLimitAsync(() => GetNotifyInfoCancel(bidCode, version));
        var notifyInfo = await ExecuteWithRateLimitAsync(() => GetNotifyInfo(bidCode, version));
        var notifyListFile = await ExecuteWithRateLimitAsync(() => GetNotifyListFile(bidCode, version));
        var bidTechRule = await ExecuteWithRateLimitAsync(() => GetBidTechRule(bidCode, version, bidType));

        return new
        {
            notifyYclr,
            notifyMeeting,
            notifyInfoCancel,
            notifyInfo,
            notifyListFile,
            bidTechRule
        };
    }

    private static void SetupLogging()
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .CreateLogger();
    }

    //Check folder
    private static Task CheckFolderAsync(string? path)
    {
        return Task.Run(() =>
        {
            if (Directory.Exists(path)) return;
            Log.Information($"Creating folder {path}");
            Directory.CreateDirectory(path ?? throw new ArgumentNullException(nameof(path)));
        });
    }

    //Create request
    private RestRequest CreateRestRequest(string url, DateTime? fromTime, DateTime? toTime, int pageNumber = 0,
        int pageSize = 10000)
    {
        var restRequest = new RestRequest(url)
        {
            Method = Method.Post,
            RequestFormat = DataFormat.Json,
            Timeout = new TimeSpan(0, 0, 30),
        };

        var payLoad = new BodyPayloadCommon
        {
            Body =
            [
                new()
                {
                    PageSize = pageSize,
                    PageNumber = pageNumber,
                    Query =
                    [
                        new()
                        {
                            Index = "es-ebid-search",
                            KeyWord = "",
                            MatchType = "all-1",
                            MatchFields =
                            [
                                "code",
                                "ten_ben_moi_thau",
                                "ma_ben_moi_thau",
                                "ten_goi_thau"
                            ],
                            Filters =
                            [
                                new()
                                {
                                    FieldName = "type", SearchType = "in", FieldValues = ["vk_tbmt"]
                                },

                                new()
                                {
                                    FieldName = "quy_trinh_ap_dung",
                                    SearchType = "in",
                                    FieldValues =
                                    [
                                        "1", "2"
                                    ]
                                },
                                new()
                                {
                                    FieldName = "ngay_dang_tai",
                                    SearchType = "range",
                                    From = fromTime?.ToString("yyyy-MM-dd'T'HH:mm:ssZ") ?? string.Empty,
                                    To = toTime?.ToString("yyyy-MM-dd'T'HH:mm:ssZ") ?? string.Empty
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        restRequest.AddJsonBody(payLoad);
        return restRequest;
    }

    //get total records
    private async Task<int> GetTotalRecords(DateTime fromTime, DateTime toTime, int pageNumber = 0,
        int pageSize = 10)
    {
        var restRequest = CreateRestRequest(UrlOrigin, fromTime, toTime, pageNumber, pageSize);
        try
        {
            var response = await _restClient.ExecuteAsync<ResponseModel<ThongBaoMoiThauLuatDauThauSearch>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data!.Body?.Page?.TotalElements ?? 0;
            }

            Log.Warning($"Warning getting total records: {response.ErrorMessage}");
            return 0;
        }
        catch (Exception e)
        {
            Log.Error($"Error getting total records: {e.Message}");
            return 0;
        }
    }

    // https://muasamcong.mpi.gov.vn/api/unau/smartsearchold/es/search
    private async Task<List<ContentThongBaoMoiThauSearch>> Search(DateTime fromTime, DateTime toTime,
        int pageNumber = 0, int pageSize = 10000)
    {
        var restRequest = CreateRestRequest(UrlOrigin, fromTime, toTime, pageNumber, pageSize);
        try
        {
            var response =
                await _restClient.ExecuteAsync<ResponseModel<ContentThongBaoMoiThauSearch>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data!.Body?.Page?.Content ?? [];
            }

            Log.Warning($"Warning searching data: {response.ErrorMessage}");

            return new List<ContentThongBaoMoiThauSearch>();
        }
        catch (Exception e)
        {
            Log.Error($"Error searching data: {e.Message}");
            return new List<ContentThongBaoMoiThauSearch>();
        }
    }

    //https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bido-notify/get-notify-yclr
    private async Task<GetNotifyYclr> GetNotifyYclr(string bidNo, string bidTurnNo)
    {
        var restRequest =
            new RestRequest("https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bido-notify/get-notify-info-vk")
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

        var payLoad = new
        {
            Body = new
            {
                bidNo = bidNo,
                bidTurnNo = bidTurnNo
            }
        };

        restRequest.AddJsonBody(payLoad);
        try
        {
            var response = await _restClient.ExecuteAsync<ApiResponse<GetNotifyYclr>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data.Body;
            }

            Log.Warning($"Warning getting notify yclr: {response.ErrorMessage}");
            return new();
        }
        catch (Exception e)
        {
            Log.Error($"Error getting notify yclr: {e.Message}");
            return new();
        }
    }

    //https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bido-notify/get-bid-tech-rule
    //{"body":{"bidNo":"20220927704","bidTurnNo":"01","bidType":"20"}}
    private async Task<BidTechnicalResponse> GetBidTechRule(string bidNo, string bidTurnNo, string bidType)
    {
        var restRequest =
            new RestRequest("https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bido-notify/get-bid-tech-rule")
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

        var payLoad = new
        {
            Body = new
            {
                bidNo = bidNo,
                bidTurnNo = bidTurnNo,
                bidType = bidType
            }
        };

        restRequest.AddJsonBody(payLoad);
        try
        {
            var response = await _restClient.ExecuteAsync<ApiResponse<BidTechnicalResponse>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data.Body;
            }

            Log.Warning($"Warning getting bid tech rule: {response.ErrorMessage}");
            return new();
        }
        catch (Exception e)
        {
            Log.Error($"Error getting bid tech rule: {e.Message}");
            return new();
        }
    }


    //https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bido-notify/get-notify-meeting
    // {"body":{"bidNo":"20220951872","bidTurnNo":"00"}}
    private async Task<GetNotifyMeeting> GetNotifyMeeting(string bidNo, string bidTurnNo)
    {
        var restRequest =
            new RestRequest("https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bido-notify/get-notify-meeting")
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

        var payLoad = new
        {
            Body = new
            {
                bidNo = bidNo,
                bidTurnNo = bidTurnNo
            }
        };

        restRequest.AddJsonBody(payLoad);
        try
        {
            var response = await _restClient.ExecuteAsync<ApiResponse<GetNotifyMeeting>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data.Body;
            }

            Log.Warning($"Warning getting notify meeting: {response.ErrorMessage}");
            return new GetNotifyMeeting();
        }
        catch (Exception e)
        {
            Log.Error($"Error getting notify meeting: {e.Message}");
            return new GetNotifyMeeting();
        }
    }

    //https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bido-notify/get-notify-info-cancel
    //{"body":{"bidNo":"20220951872","bidTurnNo":"00"}}
    private async Task<GetNotifyInfoCancel> GetNotifyInfoCancel(string bidNo, string bidTurnNo)
    {
        var restRequest =
            new RestRequest("https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bido-notify/get-notify-info-cancel")
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

        var payLoad = new
        {
            Body = new
            {
                bidNo = bidNo,
                bidTurnNo = bidTurnNo
            }
        };

        restRequest.AddJsonBody(payLoad);
        try
        {
            var response = await _restClient.ExecuteAsync<ApiResponse<GetNotifyInfoCancel>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data.Body;
            }

            Log.Warning($"Warning getting notify info cancel: {response.ErrorMessage}");
            return new GetNotifyInfoCancel();
        }
        catch (Exception e)
        {
            Log.Error($"Error getting notify info cancel: {e.Message}");
            return new GetNotifyInfoCancel();
        }
    }

    //https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bido-notify/get-notify-info
    //{"body":{"bidNo":"20220951872","bidTurnNo":"00"}}
    private async Task<BidDetailResponse> GetNotifyInfo(string bidNo, string bidTurnNo)
    {
        var restRequest =
            new RestRequest("https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bido-notify/get-notify-info-vk")
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

        var payLoad = new
        {
            Body = new
            {
                bidNo = bidNo,
                bidTurnNo = bidTurnNo
            }
        };

        restRequest.AddJsonBody(payLoad);
        try
        {
            var response = await _restClient.ExecuteAsync<ApiResponse<BidDetailResponse>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data.Body;
            }

            Log.Warning($"Warning getting notify info: {response.ErrorMessage}");
            return new();
        }
        catch (Exception e)
        {
            Log.Error($"Error getting notify info: {e.Message}");
            return new();
        }
    }


    private async Task<BidFileResponse> GetNotifyListFile(string bidNo, string bidTurnNo)
    {
        var restRequest =
            new RestRequest("https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/pre-notify/get-list-file-attach-vk")
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

        var payLoad = new
        {
            Body = new
            {
                bidNo = bidNo,
                bidTurnNo = bidTurnNo
            }
        };

        restRequest.AddJsonBody(payLoad);
        try
        {
            var response = await _restClient.ExecuteAsync<ApiResponse<BidFileResponse>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data.Body;
            }

            Log.Warning($"Warning getting notify list file: {response.ErrorMessage}");
            return new();
        }
        catch (Exception e)
        {
            Log.Error($"Error getting notify list file: {e.Message}");
            return new();
        }
    }

    // Get versions to fetch
    private List<string> GetVersionsToFetch(string currentVersion)
    {
        var versions = new List<string> { currentVersion };
        int versionNumber = int.Parse(currentVersion);

        while (versionNumber > 0)
        {
            Log.Information($"Adding version {versionNumber}");
            versionNumber--;
            versions.Add(versionNumber.ToString("D2"));
        }

        return versions;
    }

    private Task WriteJsonToFileAsync(StreamWriter streamWriter, string json)
    {
        return streamWriter.WriteAsync(json);
    }

    private DateTime? GetLatestProcessedDate()
    {
        var dataDir = Path.Combine(Directory.GetCurrentDirectory(), OutputFolder);

        if (!Directory.Exists(dataDir)) return null;

        Log.Information($"Checking for latest processed date in {dataDir}");
        var years = Directory.GetDirectories(dataDir)
            .Select(Path.GetFileName)
            .Where(y => int.TryParse(y, out _))
            .OrderByDescending(y => y);

        foreach (var year in years)
        {
            var yearDir = Path.Combine(dataDir, year);
            var months = Directory.GetDirectories(yearDir)
                .Select(Path.GetFileName)
                .Where(m => int.TryParse(m, out _))
                .OrderByDescending(m => m);

            foreach (var month in months)
            {
                var monthDir = Path.Combine(yearDir, month);
                var files = Directory.GetFiles(monthDir, "*.json")
                    .Select(Path.GetFileNameWithoutExtension)
                    .OrderByDescending(f => f);

                if (files.Any())
                {
                    var latestFile = files.First();
                    var latestDate = latestFile.Split('_')[0];
                    return DateTime.TryParseExact(latestDate, "yyyy-MM-dd", null,
                        System.Globalization.DateTimeStyles.None, out var date)
                        ? date
                        : null;
                }
            }
        }

        return null;
    }

    private async Task SaveDataToFileAsync(List<object> contents, DateTime fromTime, DateTime toTime)
    {
        var path = Path.Combine(Directory.GetCurrentDirectory(), OutputFolder, $"{fromTime:yyyy}",
            $"{fromTime:MM}");
        await CheckFolderAsync(path);

        var fileName = $"{fromTime:yyyy-MM-dd_HH-mm-ss}_to_{toTime:yyyy-MM-dd_HH-mm-ss}.json";
        var fullPath = Path.Combine(path, fileName);

        try
        {
            Log.Information($"Saving data to file {fullPath}");
            await using var streamWriter = new StreamWriter(fullPath);
            var json = JsonConvert.SerializeObject(contents, Formatting.Indented);
            await WriteJsonToFileAsync(streamWriter, json);
        }
        catch (Exception e)
        {
            Log.Error(e, "Error saving data to file");
        }
    }
}