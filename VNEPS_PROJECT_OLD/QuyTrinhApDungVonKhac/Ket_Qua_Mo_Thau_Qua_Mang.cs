using Newtonsoft.Json;
using RestSharp;
using Serilog;
using VNEPS_PROJECT_OLD.Models.Payload;
using VNEPS_PROJECT_OLD.Models.VonKhac;

namespace VNEPS_PROJECT_OLD.QuyTrinhApDungVonKhac;

public class Ket_Qua_Mo_Thau_Qua_Mang
{
    private const string UrlOrigin = "https://muasamcong.mpi.gov.vn/api/unau/smartsearchold/es/search";
    private readonly IRestClient _restClient = new RestClient();
    private readonly int _maxRecords = 10000;
    private const string OutputFolder = "Data/QuyTrinhApDungVonKhac/KetQuaMoThauQuaMang";
    private readonly SemaphoreSlim _rateLimitSemaphore = new(5, 5); // Limit concurrent requests
    private const int RequestDelayMs = 1000;

    public Ket_Qua_Mo_Thau_Qua_Mang()
    {
        SetupLogging();
        Log.Information("Starting data processing...");
    }

    private async Task<T> ExecuteWithRateLimitAsync<T>(Func<Task<T>> apiCall)
    {
        await _rateLimitSemaphore.WaitAsync();
        try
        {
            Log.Information("Executing API call... {ApiCall}", apiCall.Method.Name);
            await Task.Delay(RequestDelayMs); // Add delay between requests
            return await apiCall();
        }
        finally
        {
            _rateLimitSemaphore.Release();
        }
    }

    private static void SetupLogging()
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .CreateLogger();
    }

    public async Task ProcessAsync()
    {
        Log.Information($"Processing data ");
        await FetchAndProcessDataForRange();
    }

    private static Task CheckFolderAsync(string? path)
    {
        return Task.Run(() =>
        {
            if (Directory.Exists(path)) return;
            Log.Information($"Creating folder {path}");
            Directory.CreateDirectory(path ?? throw new ArgumentNullException(nameof(path)));
        });
    }

    private async Task FetchAndProcessDataForRange()
    {
        var listObjectSave = new List<object>();
        var semaphore = new SemaphoreSlim(10); // Limit to 10 concurrent tasks

        try
        {
            var count = await ExecuteWithRateLimitAsync(() => GetTotalRecords());
            Log.Information($"Total records: {count}");
            if (count == 0)
            {
                Log.Information($"No data found");
                return;
            }

            var countTotalPage = count / _maxRecords + 1;
            Log.Information($"Total pages: {countTotalPage}");

            for (var i = 0; i < countTotalPage; i++)
            {
                Log.Information($"Processing page {i + 1} of {countTotalPage}");
                var lstResult = await ExecuteWithRateLimitAsync(() => Search(i, _maxRecords));
                if (lstResult.Count == 0)
                {
                    Log.Information("No data found");
                    return;
                }

                Log.Information($"Processing data for {lstResult.Count} records");

                var tasks = lstResult.Select(async searchReSearch =>
                {
                    await semaphore.WaitAsync();
                    try
                    {
                        Log.Information($"Processing data for {searchReSearch.Code} - {searchReSearch.Version}");
                        var versionToFetch = GetVersionsToFetch(searchReSearch.Version!);

                        var dataVersion = await Task.WhenAll(versionToFetch.Select(async version =>
                            await FetchDetailedDataForVersion(searchReSearch.Code!, version)));

                        return new
                        {
                            thongTinTuDanhSachTimKiem = searchReSearch,
                            dataVersion
                        };
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }).ToList();

                var processedResults = await Task.WhenAll(tasks);
                listObjectSave.AddRange(processedResults);
            }
        }
        catch (Exception e)
        {
            Log.Error(e, "Error processing data");
        }

        Log.Information($"Saving data ");
        await SaveDataToFileAsync(listObjectSave);
    }

    private async Task<object> FetchDetailedDataForVersion(string bidCode, string version)
    {
        var winningBidInfo = await ExecuteWithRateLimitAsync(async () =>
        {
            Log.Information($"Fetching data for {bidCode} - {version}");
            var cancelBidDetail = await GetDetail(bidCode, version);
            var htmlInfo = await GetHtmlInfo(bidCode, version);
            var bidSuccessOpen = await GetBidSuccessOpen(bidCode, version);
            return new
            {
                cancelBidDetail,
                htmlInfo,
                bidSuccessOpen
            };
        });

        return new
        {
            winningBidInfo
        };
    }

    private RestRequest CreateRestRequest(string url, int pageNumber = 0,
        int pageSize = 10000)
    {
        var restRequest = new RestRequest(url)
        {
            Method = Method.Post,
            RequestFormat = DataFormat.Json,
            Timeout = new TimeSpan(0, 0, 30),
        };

        var payLoad = new BodyPayloadCommon
        {
            Body =
            [
                new Body
                {
                    PageSize = pageSize,
                    PageNumber = pageNumber,
                    Query =
                    [
                        new Query
                        {
                            Index = "es-ebid-search",
                            KeyWord = "",
                            MatchType = "all-1",
                            MatchFields =
                                ["code", "ten_goi_thau"],
                            Filters =
                            [
                                new Filter
                                {
                                    FieldName = "type", SearchType = "in", FieldValues = ["vk_kqmtqm"]
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        restRequest.AddJsonBody(payLoad);
        return restRequest;
    }

    private async Task<int> GetTotalRecords(int pageNumber = 0,
        int pageSize = 10)
    {
        var restRequest = CreateRestRequest(UrlOrigin, pageNumber, pageSize);
        try
        {
            Log.Information("Getting total records.. in {fromTime:yyyy-MM-dd} - {toTime:yyyy-MM-dd}");
            var response = await _restClient.ExecuteAsync<ResponseModel<ContentVonKhacSearch>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data!.Body?.Page?.TotalElements ?? 0;
            }

            Log.Warning($"Warning getting total records: {response.ErrorMessage}");
            return 0;
        }
        catch (Exception e)
        {
            Log.Error($"Error getting total records: {e.Message}");
            return 0;
        }
    }

    private async Task<List<ContentKetQuaMoThauQuaMang>> Search(int pageNumber = 0, int pageSize = 10000)
    {
        var restRequest = CreateRestRequest(UrlOrigin, pageNumber, pageSize);
        try
        {
            Log.Information("Searching data.. in {fromTime:yyyy-MM-dd} - {toTime:yyyy-MM-dd}");
            var response =
                await _restClient.ExecuteAsync<ResponseModel<ContentKetQuaMoThauQuaMang>>(restRequest);
            if (response.IsSuccessful)
            {
                return response.Data!.Body?.Page?.Content ?? [];
            }

            Log.Warning($"Warning searching data: {response.ErrorMessage}");

            return new();
        }
        catch (Exception e)
        {
            Log.Error($"Error searching data: {e.Message}");
            return new();
        }
    }

    private async Task<BodyCancelBidDetail> GetDetail(string bidPlanProjectId, string bidTurnNo)
    {
        Log.Information($"Get detail for {bidPlanProjectId} - {bidTurnNo}");
        var restRequest =
            new RestRequest(
                "https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bid-open-internet/cancel-bid-detail")
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

        //{"body":{"bidNo":"20221003307","bidVersion":"00"}}
        var payLoad = new
        {
            body = new
            {
                bidNo = bidPlanProjectId,
                bidTurnNo = $"{bidTurnNo}"
            }
        };

        restRequest.AddJsonBody(payLoad);
        try
        {
            var response =
                await _restClient.ExecuteAsync(restRequest);
            if (response is { IsSuccessful: true })
            {
                var data =
                    JsonConvert.DeserializeObject<ApiResponse<BodyCancelBidDetail>>(response.Content);
                return data.Body;
            }

            Log.Warning($"Warning searching data: {response.ErrorMessage}");

            return new();
        }
        catch (Exception e)
        {
            Log.Error($"Error searching data: {e.Message}");
            return new();
        }
    }
    //GET HTML - INFO 
    // https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bid-open-internet/get-htmt-info
    // {"body":{"bidNo":"20211259205","bidTurnNo":"00","bidType":"VK"}}

    public async Task<BodyHtmlInfo> GetHtmlInfo(string bidPlanProjectId, string bidTurnNo)
    {
        Log.Information($"Get detail for {bidPlanProjectId} - {bidTurnNo}");
        var restRequest =
            new RestRequest(
                "https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bid-open-internet/get-htmt-info")
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

        //{"body":{"bidNo":"20221003307","bidVersion":"00"}}
        var payLoad = new
        {
            body = new
            {
                bidNo = bidPlanProjectId,
                bidTurnNo = $"{bidTurnNo}",
                bidType = "VK"
            }
        };

        restRequest.AddJsonBody(payLoad);
        try
        {
            var response =
                await _restClient.ExecuteAsync(restRequest);
            if (response is { IsSuccessful: true })
            {
                var data =
                    JsonConvert.DeserializeObject<ApiResponse<BodyHtmlInfo>>(response.Content);
                return data.Body;
            }

            Log.Warning($"Warning searching data: {response.ErrorMessage}");

            return new();
        }
        catch (Exception e)
        {
            Log.Error($"Error searching data: {e.Message}");
            return new();
        }
    }
    //bid success open 
    //https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bid-open-internet/bid-success-open
    //{"body":{"bidNo":"20211259205","bidTurnNo":"00","bidType":"VK"}}

    public async Task<BidSuccessOpen> GetBidSuccessOpen(string bidPlanProjectId, string bidTurnNo)
    {
        Log.Information($"Get detail for {bidPlanProjectId} - {bidTurnNo}");
        var restRequest =
            new RestRequest(
                "https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bid-open-internet/bid-success-open")
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

        var payLoad = new
        {
            body = new
            {
                bidNo = bidPlanProjectId,
                bidTurnNo = $"{bidTurnNo}",
                bidType = "VK"
            }
        };

        restRequest.AddJsonBody(payLoad);
        try
        {
            var response =
                await _restClient.ExecuteAsync(restRequest);
            if (response is { IsSuccessful: true })
            {
                var data =
                    JsonConvert.DeserializeObject<ApiResponse<BidSuccessOpen>>(response.Content);
                return data.Body;
            }

            Log.Warning($"Warning searching data: {response.ErrorMessage}");

            return new();
        }
        catch (Exception e)
        {
            Log.Error($"Error searching data: {e.Message}");
            return new();
        }
    }


    private List<string> GetVersionsToFetch(string currentVersion)
    {
        Log.Information($"Getting versions to fetch for {currentVersion}");
        var versions = new List<string> { currentVersion };
        int versionNumber = int.Parse(currentVersion);

        while (versionNumber > 0)
        {
            Log.Information($"Adding version {versionNumber}");
            versionNumber--;
            versions.Add(versionNumber.ToString("D2"));
        }

        return versions;
    }

    private Task WriteJsonToFileAsync(StreamWriter streamWriter, string json)
    {
        Log.Information($"Writing data to file");
        return streamWriter.WriteAsync(json);
    }


    private async Task SaveDataToFileAsync(List<object> contents)
    {
        var path = Path.Combine(Directory.GetCurrentDirectory(), OutputFolder);
        await CheckFolderAsync(path);

        var fileName = $"quytrinhapdungvonkhac_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.json";
        var fullPath = Path.Combine(path, fileName);

        try
        {
            Log.Information($"Saving data to file {fullPath}");
            await using var streamWriter = new StreamWriter(fullPath);
            var json = JsonConvert.SerializeObject(contents, Formatting.Indented);
            await WriteJsonToFileAsync(streamWriter, json);
        }
        catch (Exception e)
        {
            Log.Error(e, "Error saving data to file");
        }
    }
}