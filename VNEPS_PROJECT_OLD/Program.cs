using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using VNEPS_PROJECT_OLD.Extensions;
using VNEPS_PROJECT_OLD.QuyTrinhApDungVonKhac;
using Danh_Sach_Ngan = VNEPS_PROJECT_OLD.QuyTrinhApDungLuatDauThau.Danh_Sach_Ngan;
using Ket_Qua_Dau_Thau_Khong_Qua_Mang = VNEPS_PROJECT_OLD.QuyTrinhApDungLuatDauThau.Ket_Qua_Dau_Thau_Khong_Qua_Mang;
using Ket_Qua_Dau_Thau_Qua_Mang = VNEPS_PROJECT_OLD.QuyTrinhApDungLuatDauThau.Ket_Qua_Dau_Thau_Qua_Mang;
using Ket_Qua_Mo_Thau_Qua_Mang = VNEPS_PROJECT_OLD.QuyTrinhApDungLuatDauThau.Ket_Qua_Mo_Thau_Qua_Mang;
using Ket_Qua_So_Tuyen = VNEPS_PROJECT_OLD.QuyTrinhApDungLuatDauThau.Ket_Qua_So_Tuyen;
using Thong_Bao_Moi_Thau = VNEPS_PROJECT_OLD.QuyTrinhApDungLuatDauThau.Thong_Bao_Moi_Thau;
using Thong_Bao_Moi_Thau_So_Tuyen = VNEPS_PROJECT_OLD.QuyTrinhApDungVonKhac.Thong_Bao_Moi_Thau_So_Tuyen;

namespace VNEPS_PROJECT_OLD
{
    public class Program
    {
        static async Task Main()
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}.json",
                    optional: true)
                .Build();

            Console.OutputEncoding = Encoding.UTF8;
            var serviceCollection = new ServiceCollection();
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            if (connectionString == null)
            {
                throw new ArgumentNullException($"Connection string is null");
            }

            RegisterService.ConfigureServices(serviceCollection, connectionString);
            // await using var connection = new NpgsqlConnection(connectionString);
            // await connection.OpenAsync();

            while (true)
            {
                Console.WriteLine("\nPlease select an option:");
                Console.WriteLine("==================================Dự án đầu tư===================================");
                Console.WriteLine("1. Dự án đầu tư phát triển");
                Console.WriteLine("==================================Kế hoạch lựa chọn nhà thầu=====================");
                Console.WriteLine("2. Kế hoạch lựa chọn nhà thầu");
                Console.WriteLine("===================== Quy trinh ap dung luat dau thau ========================");
                Console.WriteLine("3. Quy trinh ap dung luat dau thau - Thong bao moi thau so tuyen");
                Console.WriteLine("4. Quy trinh ap dung luat dau thau - Thong bao moi thau");
                Console.WriteLine("5. Quy trinh ap dung luat dau thau - Danh sach ngan");
                Console.WriteLine("6. Quy trinh ap dung luat dau thau - Ket qua so tuyen");
                Console.WriteLine("7. Quy trinh ap dung luat dau thau - Kết quả mở thầu qua mạng");
                Console.WriteLine("8. Quy trinh ap dung luat dau thau - Kết quả đấu thầu qua mạng");
                Console.WriteLine("9. Quy trinh ap dung luat dau thau - Kết quả đấu thầu không qua mạng");
                Console.WriteLine("10. Quy trinh ap dung luat dau thau - Thông tin điều chỉnh");
                Console.WriteLine("====================Quy trình áp dụng hiệp định CPTPP=====================");
                //Thông báo mời thầu
                Console.WriteLine("11. Quy trình áp dụng hiệp định CPTPP - Thông báo mời thầu");
                //Kết quả đấu thầu không qua mạng
                Console.WriteLine("12. Quy trình áp dụng hiệp định CPTPP - Kết quả đấu thầu không qua mạng");
                //Quy trình áp dụng vốn khác
                Console.WriteLine("==================================Quy trình áp dụng vốn khác=====================");
                Console.WriteLine("13. Quy trình áp dụng vốn khác - Kế hoạch lựa chọn nhà thầu");
                Console.WriteLine("14. Quy trình áp dụng vốn khác - Thông báo mời sơ tuyển");
                Console.WriteLine("15. Quy trình áp dụng vốn khác - Thông báo mời thầu");
                Console.WriteLine("16. Quy trình áp dụng vốn khác - Danh sách ngắn");
                Console.WriteLine("17. Quy trình áp dụng vốn khác - Kết quả sơ tuyển");
                Console.WriteLine("18. Quy trình áp dụng vốn khác - Kết quả mở thầu qua mạng");
                Console.WriteLine("19. Quy trình áp dụng vốn khác - Kết quả đấu thầu qua mạng");
                Console.WriteLine("20. Quy trình áp dụng vốn khác - Kết quả đấu thầu không qua mạng");
                //LỰA CHỌN NHÀ ĐẦU TƯ
                Console.WriteLine("==================================Lựa chọn nhà đầu tư=====================");
                Console.WriteLine("21. Nội dung quyết định chủ trương đầu tư dự án PPP");
                Console.WriteLine("22. Khảo sát sự quan tâm của nhà đầu tư");
                Console.WriteLine("23. Quyết định phê duyệt dự án PPP");
                Console.WriteLine("24. Công bố Danh mục dự án");
                Console.WriteLine("25. Kết quả mở hồ sơ đăng ký thực hiện dự án đầu tư có sử dụng đất");
                Console.WriteLine(
                    "26. Kết quả đánh giá sơ bộ năng lực kinh nghiệm đối với dự án đầu tư có sử dụng đất");
                Console.WriteLine("27. Thông báo mời sơ tuyển");
                //ket qua so tuyen
                Console.WriteLine("28. Kết quả sơ tuyển");
                Console.WriteLine("29. Kế hoạch lựa chọn nhà đầu tư");
                Console.WriteLine("30. Thông báo mời thầu");
                Console.WriteLine("31. Kết quả lựa chọn nhà đầu tư");
                Console.WriteLine("32. Hợp đồng dự án");

                Console.WriteLine("==================================END=====================");
                Console.WriteLine("0. Exit");

                Console.Write("Enter your choice : ");

                var choice = Console.ReadLine();

                switch (choice)
                {
                    case "0":
                        Console.WriteLine("Exiting the application...");
                        return;

                    case "1":
                        var getDataProjectOld = new GetDataProjectOld();
                        await getDataProjectOld.ProcessAsync();
                        break;

                    case "2":
                        var getDataPlanOld = new GetDataPlanOld();
                        await getDataPlanOld.ProcessAsync();
                        break;

                    case "3":
                        var getThongBaoMoiThauSoTuyen = new QuyTrinhApDungLuatDauThau.Thong_Bao_Moi_Thau_So_Tuyen();
                        await getThongBaoMoiThauSoTuyen.ProcessAsync();
                        break;

                    case "4":
                        var getThongBaoMoiThau = new Thong_Bao_Moi_Thau();
                        await getThongBaoMoiThau.ProcessAsync();
                        break;

                    case "5":
                        var danhSachNgan = new Danh_Sach_Ngan();
                        await danhSachNgan.ProcessAsync();
                        break;

                    case "6":
                        var ketQuaSoTuyen = new Ket_Qua_So_Tuyen();
                        await ketQuaSoTuyen.ProcessAsync();
                        break;

                    case "7":
                        var ketQuaMoThauQuaMang = new Ket_Qua_Mo_Thau_Qua_Mang();
                        await ketQuaMoThauQuaMang.ProcessAsync();
                        break;

                    case "8":
                        var ketQuaDauThauQuaMang = new Ket_Qua_Dau_Thau_Qua_Mang();
                        await ketQuaDauThauQuaMang.ProcessAsync();
                        break;

                    case "9":
                        var ketQuaDauThauKhongQuaMang = new Ket_Qua_Dau_Thau_Khong_Qua_Mang();
                        await ketQuaDauThauKhongQuaMang.ProcessAsync();
                        break;

                    case "13":
                        var ketHoachLuaChonNha = new Ket_Hoach_Lua_Chon_Nha_Thau();
                        await ketHoachLuaChonNha.ProcessAsync();
                        break;

                    case "14":
                        var thongBaoMoiSoTuyen = new Thong_Bao_Moi_Thau_So_Tuyen();
                        await thongBaoMoiSoTuyen.ProcessAsync();
                        break;

                    case "15":
                        var thongBaoMoiThau = new QuyTrinhApDungVonKhac.Thong_Bao_Moi_Thau();
                        await thongBaoMoiThau.ProcessAsync();
                        break;

                    case "16":
                        var danhSachNganVonKhac = new QuyTrinhApDungVonKhac.Danh_Sach_Ngan();
                        await danhSachNganVonKhac.ProcessAsync();
                        break;

                    case "17":
                        var ketQuaSoTuyenVonKhac = new QuyTrinhApDungVonKhac.Ket_Qua_So_Tuyen();
                        await ketQuaSoTuyenVonKhac.ProcessAsync();
                        break;

                    case "18":
                        var ketQuaMoThauQuaMangVonKhac = new QuyTrinhApDungVonKhac.Ket_Qua_Mo_Thau_Qua_Mang();
                        await ketQuaMoThauQuaMangVonKhac.ProcessAsync();
                        break;

                    case "19":
                        var ketQuaDauThauQuaMangVonKhac = new QuyTrinhApDungVonKhac.Ket_Qua_Dau_Thau_Qua_Mang();
                        await ketQuaDauThauQuaMangVonKhac.ProcessAsync();
                        break;

                    case "20":
                        var noiDungQuyetDinh = new QuyTrinhApDungVonKhac.Ket_Qua_Dau_Thau_Khong_Qua_Mang();
                        await noiDungQuyetDinh.ProcessAsync();
                        break;

                    case "21":
                        var noiDungQuyetDinhChuTruong = new LuaChonNhaDauTu.Noi_Dung_Quyet_Dinh();
                        await noiDungQuyetDinhChuTruong.ProcessAsync();
                        break;

                    case "22":
                        var khaoSatSuQuanTam = new LuaChonNhaDauTu.Khao_Sat_Su_Quan_Tam_Cua_Nha_Dau_Tu();
                        await khaoSatSuQuanTam.ProcessAsync();
                        break;

                    case "23":
                        var quyetDinhPheDuyet = new LuaChonNhaDauTu.Quyet_Dinh_Phe_Duyet_Du_An_PPP();
                        await quyetDinhPheDuyet.ProcessAsync();
                        break;

                    case "24":
                        var congBoDanhMuc = new LuaChonNhaDauTu.Cong_Bo_Danh_Muc_Du_An();
                        await congBoDanhMuc.ProcessAsync();
                        break;

                    case "25":
                        var ketQuaMoHoSo = new LuaChonNhaDauTu.Ket_Qua_Mo_Ho_So_Dang_Ky_Thuc_Hien_Du_An();
                        await ketQuaMoHoSo.ProcessAsync();
                        break;

                    case "26":
                        var ketQuaDanhGia = new LuaChonNhaDauTu.Ket_Qua_Danh_Gia_So_Bo_Nang_Luc();
                        await ketQuaDanhGia.ProcessAsync();
                        break;

                    case "27":
                        var thongBaoMoiSoTuyenNhaDauTu = new LuaChonNhaDauTu.Thong_Bao_Moi_Thau_So_Tuyen();
                        await thongBaoMoiSoTuyenNhaDauTu.ProcessAsync();
                        break;

                    case "28":
                        var ketQuaSoTuyenNhaDauTu = new LuaChonNhaDauTu.Ket_Qua_So_Tuyen();
                        await ketQuaSoTuyenNhaDauTu.ProcessAsync();
                        break;
                    case "29":
                        var ketHoachLuaChonNhaDauTu = new LuaChonNhaDauTu.Ke_Hoach_Lua_Chon_Nha_Dau_Tu();
                        await ketHoachLuaChonNhaDauTu.ProcessAsync();
                        break;
                    case "30":
                        var thongBaoMoiThauNhaDauTu = new LuaChonNhaDauTu.Thong_Bao_Moi_Thau();
                        await thongBaoMoiThauNhaDauTu.ProcessAsync();
                        break;
                    case "31":
                        var ketQuaLuaChonNhaDauTu = new LuaChonNhaDauTu.Ket_Qua_Lua_Chon_Nha_Dau_Tu();
                        await ketQuaLuaChonNhaDauTu.ProcessAsync();
                        break;
                    case "32":
                        var hopDongDuAn = new LuaChonNhaDauTu.Hop_Dong_Du_An();
                        await hopDongDuAn.ProcessAsync();
                        break;

                    default:
                        Console.WriteLine("Invalid option. Please try again.");
                        break;
                }
            }
        }
    }
}