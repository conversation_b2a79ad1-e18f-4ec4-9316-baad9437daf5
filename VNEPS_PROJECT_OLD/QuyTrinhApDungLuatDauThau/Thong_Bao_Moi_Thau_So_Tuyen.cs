using System.Collections.Concurrent;
using System.Threading.Channels;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using VNEPS_PROJECT_OLD.Models.Enum;
using VNEPS_PROJECT_OLD.Models.LuatDauThau.ThongBaoMoiThauSoTuyen;
using VNEPS_PROJECT_OLD.Models.Payload;

namespace VNEPS_PROJECT_OLD.QuyTrinhApDungLuatDauThau;

public class Thong_Bao_Moi_Thau_So_Tuyen
{
    private const string UrlOrigin = "https://muasamcong.mpi.gov.vn/api/unau/smartsearchold/es/search";
    private readonly IRestClient _restClient = new RestClient();
    private const int MaxRecords = 10000;
    private const string OutputFolder = "Data/QuyTrinhApDungLuatDauThau/ThongBaoMoiThauSoTuyen";
    private const string ErrorOutputFolder = "Data/QuyTrinhApDungLuatDauThau/ThongBaoMoiThauSoTuyen/Errors";


    private readonly SemaphoreSlim _rateLimiter;
    private readonly int _maxConcurrentTasks;
    private readonly int _delayBetweenRequestsMs;

    //Constructor

    public Thong_Bao_Moi_Thau_So_Tuyen(
        int maxConcurrentRequests = 5,
        int delayBetweenRequestsMs = 1000)
    {
        _maxConcurrentTasks = maxConcurrentRequests;
        _delayBetweenRequestsMs = delayBetweenRequestsMs;
        _rateLimiter = new SemaphoreSlim(maxConcurrentRequests);

        SetupLogging();
        Log.Information("Starting data processing...");
    }

    private async Task<T> ExecuteRateLimitedRequestAsync<T>(Func<Task<T>> requestAction)
    {
        await _rateLimiter.WaitAsync();
        try
        {
            // Random delay to prevent predictable request patterns
            await Task.Delay(new Random().Next(_delayBetweenRequestsMs, _delayBetweenRequestsMs * 2));

            return await requestAction();
        }
        catch (Exception ex)
        {
            Log.Error($"Request failed: {ex.Message}");
            throw;
        }
        finally
        {
            _rateLimiter.Release();
        }
    }

    public async Task ProcessAsync()
    {
        try
        {
            await FetchAndProcessDataForRange();
        }
        catch (Exception ex)
        {
            Log.Error($"Error processing data: {ex.Message}");
            throw;
        }
    }


    //Fetch and process data for range
    private async Task FetchAndProcessDataForRange()
    {
        var searchResults = await Search();
        var outputFileName = $"thong_bao_moi_thau_{DateTime.Now:yyyyMMdd_HHmmss}.json";
        var errorFileName = $"errors_{DateTime.Now:yyyyMMdd_HHmmss}.json";

        // Create output directories
        var outputPath = Path.Combine(Directory.GetCurrentDirectory(), OutputFolder, outputFileName);
        var errorPath = Path.Combine(Directory.GetCurrentDirectory(), ErrorOutputFolder, errorFileName);

        await CheckFolderAsync(Path.GetDirectoryName(outputPath));
        await CheckFolderAsync(Path.GetDirectoryName(errorPath));

        // Ensure files exist
        await using (File.Create(outputPath))
        {
        }

        await using (File.Create(errorPath))
        {
        }

        // Use a channel for incremental saving
        var channelOptions = new BoundedChannelOptions(10)
        {
            FullMode = BoundedChannelFullMode.Wait
        };
        var dataChannel = Channel.CreateBounded<object>(channelOptions);
        var errorChannel = Channel.CreateBounded<object>(channelOptions);

        // Start incremental saving tasks
        var dataSaveTask = IncrementalSaveAsync(dataChannel.Reader, outputPath);
        var errorSaveTask = IncrementalSaveAsync(errorChannel.Reader, errorPath);

        // Process search results
        await Parallel.ForEachAsync(
            searchResults,
            new ParallelOptions { MaxDegreeOfParallelism = _maxConcurrentTasks },
            async (searchResult, token) =>
            {
                var listDetailByVersion = new List<object>();
                var processResult = new
                {
                    SearchResult = searchResult,
                    Errors = new List<string>()
                };

                try
                {
                    var lstVersions = GetVersionsToFetch(searchResult.Version!);

                    // Parallel processing of versions
                    var versionTasks = lstVersions.Select(async version =>
                    {
                        try
                        {
                            var detail = await GetDetailAsync(searchResult.Code!, version);
                            var succ = await GetSuccAsync(detail.BidType, detail.SuccPqMethod);
                            var listFileAttach = await GetListFileAttachAsync(detail.BidNo, version);
                            var infoYclr = await GetInfoYclrAsync(detail.BidNo, version);

                            return new
                            {
                                detail,
                                succ,
                                listFileAttach,
                                infoYclr
                            };
                        }
                        catch (Exception ex)
                        {
                            processResult.Errors.Add($"Error processing version {version}: {ex.Message}");
                            return null;
                        }
                    });

                    var versionResults = await Task.WhenAll(versionTasks);
                    listDetailByVersion.AddRange(versionResults.Where(r => r != null)!);
                }
                catch (Exception ex)
                {
                    processResult.Errors.Add($"Error processing search result: {ex.Message}");
                }

                var objectJsonSave = new
                {
                    thongTinLayTuDanhSachTimKiem = searchResult,
                    detailByVersion = listDetailByVersion
                };

                // If there are any errors, save to error file
                if (processResult.Errors.Any())
                {
                    var errorObject = new
                    {
                        processResult.SearchResult,
                        Errors = processResult.Errors
                    };
                    await errorChannel.Writer.WriteAsync(errorObject, token);
                }

                // Save successful results
                await dataChannel.Writer.WriteAsync(objectJsonSave, token);
            });

        // Signal completion of writing
        dataChannel.Writer.Complete();
        errorChannel.Writer.Complete();

        // Wait for saving to complete
        await Task.WhenAll(dataSaveTask, errorSaveTask);
    }

    private async Task IncrementalSaveAsync(
        System.Threading.Channels.ChannelReader<object> channelReader,
        string filePath)
    {
        await using var fileStream = new FileStream(filePath, FileMode.Append, FileAccess.Write, FileShare.Read);
        await using var writer = new StreamWriter(fileStream);
        await using var jsonWriter = new JsonTextWriter(writer)
        {
            Formatting = Formatting.Indented
        };

        // Initialize the array
        await writer.WriteAsync("[\n");
        bool isFirst = true;

        try
        {
            await foreach (var item in channelReader.ReadAllAsync())
            {
                if (!isFirst)
                {
                    await writer.WriteAsync(",\n");
                }
                else
                {
                    isFirst = false;
                }

                // Serialize and write each item
                var serializer = new JsonSerializer();
                serializer.Serialize(jsonWriter, item);
                await jsonWriter.FlushAsync();
            }
        }
        catch (Exception ex)
        {
            Log.Error($"Error during incremental saving to {filePath}: {ex.Message}");
            throw;
        }
        finally
        {
            // Close the array
            await writer.WriteAsync("\n]");
        }
    }

    private async Task AppendToJsonFileAsync(string filePath, List<object> newDataList)
    {
        try
        {
            await using var fileStream = new FileStream(filePath, FileMode.OpenOrCreate, FileAccess.ReadWrite);
            using var reader = new StreamReader(fileStream);

            // Read existing content
            var existingContent = await reader.ReadToEndAsync();

            // If file is empty or contains only an empty array
            List<object> existingList = string.IsNullOrWhiteSpace(existingContent)
                ? new List<object>()
                : JsonConvert.DeserializeObject<List<object>>(existingContent) ?? new List<object>();

            // Add new data
            existingList.AddRange(newDataList);

            // Write back to file
            fileStream.SetLength(0);
            fileStream.Seek(0, SeekOrigin.Begin);

            await using var writer = new StreamWriter(fileStream);
            var json = JsonConvert.SerializeObject(existingList, Formatting.Indented);
            await writer.WriteAsync(json);
        }
        catch (Exception ex)
        {
            Log.Error($"Error appending to JSON file {filePath}: {ex.Message}");
            throw;
        }
    }


    //Setup logging
    private static void SetupLogging()
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .CreateLogger();
    }

    //Check folder
    private static Task CheckFolderAsync(string? path)
    {
        return Task.Run(() =>
        {
            if (Directory.Exists(path)) return;
            Log.Information($"Creating folder {path}");
            Directory.CreateDirectory(path ?? throw new ArgumentNullException(nameof(path)));
        });
    }

    //Create request
    private RestRequest CreateRestRequest(string url)
    {
        var restRequest = new RestRequest(url)
        {
            Method = Method.Post,
            RequestFormat = DataFormat.Json,
            Timeout = new TimeSpan(0, 0, 30),
        };

        var payLoad = new BodyPayloadCommon
        {
            Body =
            [
                new()
                {
                    PageSize = MaxRecords,
                    PageNumber = 0,
                    Query =
                    [
                        new()
                        {
                            Index = "es-ebid-search",
                            KeyWord = "",
                            MatchType = "all-1",
                            MatchFields =
                            [
                                "code",
                                "ten_ben_moi_thau",
                                "ma_ben_moi_thau",
                                "name"
                            ],
                            Filters =
                            [
                                new()
                                {
                                    FieldName = "type", SearchType = "in", FieldValues = ["tbmst"]
                                },

                                new()
                                {
                                    FieldName = "linh_vuc",
                                    SearchType = "in",
                                    FieldValues =
                                    [
                                        (int)FieldBiddingLaw.HangHoa,
                                        (int)FieldBiddingLaw.XayLap,
                                        (int)FieldBiddingLaw.TuVan,
                                        (int)FieldBiddingLaw.PhiTuVan,
                                        (int)FieldBiddingLaw.HonHop
                                    ]
                                },
                            ]
                        }
                    ]
                }
            ]
        };

        restRequest.AddJsonBody(payLoad);
        return restRequest;
    }

    //Search
    private async Task<List<ThongBaoMoiThauLuatDauThauSoTuyenSearch>> Search()
    {
        return await ExecuteRateLimitedRequestAsync(async () =>
        {
            var restRequest = CreateRestRequest(UrlOrigin);
            var response =
                await _restClient.ExecuteAsync<ResponseModel<ThongBaoMoiThauLuatDauThauSoTuyenSearch>>(restRequest);

            if (response.IsSuccessful)
            {
                return response.Data!.Body?.Page?.Content ?? [];
            }

            Log.Error($"Error searching data: {response.ErrorMessage}");
            return new List<ThongBaoMoiThauLuatDauThauSoTuyenSearch>();
        });
    }

    //Get Detail
    //https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/pre-notify/get-detail
    private async Task<ThongBaoMoiThauLuatDauThauSoTuyenDetail?> GetDetailAsync(string id, string version)
    {
        return await ExecuteRateLimitedRequestAsync(async () =>
        {
            var url = "https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/pre-notify/get-detail";
            var restRequest = new RestRequest(url)
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };

            var body = new
            {
                body = new
                {
                    bidNo = $"{id}",
                    bidVersion = $"{version}"
                }
            };

            restRequest.AddJsonBody(body);

            var response =
                await _restClient
                    .ExecuteAsync<ApiResponseLawApplicationProcess<ThongBaoMoiThauLuatDauThauSoTuyenDetail>>(
                        restRequest);

            if (response is { IsSuccessful: true, Data.Body: not null })
            {
                Log.Information($"Get detail data successfully: {response.Content}");
                return response.Data!.Body.DetailDTO;
            }

            Log.Error($"Error get detail data: {response.ErrorMessage}");
            return null;
        });
    }

    //get Succ
    //https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/pre-notify/get-succ
    private async Task<ThongBaoMoiThauLuatDauThauSoTuyenSucc?> GetSuccAsync(string bidType, string succPqMethod)
    {
        return await ExecuteRateLimitedRequestAsync(async () =>
        {
            var url = "https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/pre-notify/get-succ";
            var restRequest = new RestRequest(url)
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };
            var cdClsValue = GetCdCls(bidType);
            var body = new
            {
                body = new
                {
                    cdCls = $"{cdClsValue}",
                    cd = $"{succPqMethod}"
                }
            };
            restRequest.AddJsonBody(body);

            try
            {
                var response =
                    await _restClient.ExecuteAsync<ApiResponse<ThongBaoMoiThauLuatDauThauSoTuyenSucc>>(restRequest);
                if (response.IsSuccessful)
                {
                    Log.Information($"Get succ data successfully: {response.Content}");
                    return response.Data!.Body;
                }

                Log.Warning($"Error get succ data: {response.ErrorMessage}");
                return new ThongBaoMoiThauLuatDauThauSoTuyenSucc();
            }
            catch (Exception e)
            {
                Log.Error($"Some thing not using succ data: {e.Message}");
                return new ThongBaoMoiThauLuatDauThauSoTuyenSucc();
            }
        });
    }

    //get list file attach
    //https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/pre-notify/get-list-file-attach
    private async Task<ThongBaoMoiThauLuatDauThauSoTuyenListFileAttach> GetListFileAttachAsync(string id,
        string version)
    {
        return await ExecuteRateLimitedRequestAsync(async () =>
        {
            var url = "https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/pre-notify/get-list-file-attach";

            var restRequest = new RestRequest(url)
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };
            var body = new
            {
                body = new
                {
                    bidNo = $"{id}",
                    bidVersion = $"{version}"
                }
            };
            restRequest.AddJsonBody(body);
            try
            {
                var response =
                    await _restClient.ExecuteAsync<ApiResponse<ThongBaoMoiThauLuatDauThauSoTuyenListFileAttach>>(
                        restRequest);
                if (response.IsSuccessful)
                {
                    Log.Information($"Get list file attach data successfully: {response.Content}");
                    return response.Data!.Body;
                }

                Log.Warning($"Error get list file attach data: {response.ErrorMessage}");
                return new ThongBaoMoiThauLuatDauThauSoTuyenListFileAttach();
            }
            catch (Exception e)
            {
                Log.Error($"Error get list file attach data: {e.Message}");
                return new ThongBaoMoiThauLuatDauThauSoTuyenListFileAttach();
            }
        });
    }

    //get info yclr
    //https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/pre-notify/get-info-yclr
    private async Task<ThongBaoMoiThauLuatDauThauSoTuyenInfoYclr> GetInfoYclrAsync(string id, string version)
    {
        return await ExecuteRateLimitedRequestAsync(async () =>
        {
            var url = "https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/pre-notify/get-info-yclr";
            var restRequest = new RestRequest(url)
            {
                Method = Method.Post,
                RequestFormat = DataFormat.Json,
                Timeout = new TimeSpan(0, 0, 30),
            };
            var body = new
            {
                body = new
                {
                    bidNo = $"{id}",
                    bidVersion = $"{version}"
                }
            };
            restRequest.AddJsonBody(body);
            try
            {
                var response =
                    await _restClient.ExecuteAsync<ApiResponse<ThongBaoMoiThauLuatDauThauSoTuyenInfoYclr>>(restRequest);
                if (response.IsSuccessful)
                {
                    Log.Information($"Get info yclr data successfully: {response.Content}");
                    return response.Data!.Body;
                }

                Log.Warning($"Error get info yclr data: {response.ErrorMessage}");
                return new ThongBaoMoiThauLuatDauThauSoTuyenInfoYclr();
            }
            catch (Exception e)
            {
                Log.Error($"Error get info yclr data: {e.Message}");
                return new ThongBaoMoiThauLuatDauThauSoTuyenInfoYclr();
            }
        });
    }

    // Get versions to fetch
    private List<string> GetVersionsToFetch(string currentVersion)
    {
        var versions = new List<string> { currentVersion };
        int versionNumber = int.Parse(currentVersion);

        while (versionNumber > 0)
        {
            Log.Information($"Adding version {versionNumber}");
            versionNumber--;
            versions.Add(versionNumber.ToString("D2"));
        }

        return versions;
    }

    private string GetCdCls(string bidType)
    {
        return bidType switch
        {
            "1" => "Z47",
            "3" => "S14",
            "5" => "Z10",
            "10" => "Z99",
            "12" => "Z55",
            "15" => "Z48",
            _ => ""
        };
    }
}