using System.Net;
using System.Threading.Channels;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using VNEPS_PROJECT_OLD.Models.Enum;
using VNEPS_PROJECT_OLD.Models.LuatDauThau.DanhSachNgan;
using VNEPS_PROJECT_OLD.Models.Payload;

namespace VNEPS_PROJECT_OLD.QuyTrinhApDungLuatDauThau;

public class Danh_Sach_Ngan
{
    private const string UrlOrigin = "https://muasamcong.mpi.gov.vn/api/unau/smartsearchold/es/search";
    private readonly IRestClient _restClient = new RestClient();
    private const int MaxRecords = 10000;
    private const string OutputFolder = "Data/QuyTrinhApDungLuatDauThau/DanhSachNgan";
    private const string ErrorOutputFolder = "Data/QuyTrinhApDungLuatDauThau/DanhSachNgan/Errors";


    private readonly SemaphoreSlim _rateLimiter;
    private readonly int _maxConcurrentTasks;
    private readonly int _delayBetweenRequestsMs;

    //Constructor

    public Danh_Sach_Ngan(
        int maxConcurrentRequests = 5,
        int delayBetweenRequestsMs = 1000)
    {
        _maxConcurrentTasks = maxConcurrentRequests;
        _delayBetweenRequestsMs = delayBetweenRequestsMs;
        _rateLimiter = new SemaphoreSlim(maxConcurrentRequests);

        SetupLogging();
        Log.Information("Starting data processing...");
    }

    private async Task<T> ExecuteRateLimitedRequestAsync<T>(Func<Task<T>> requestAction)
    {
        await _rateLimiter.WaitAsync();
        try
        {
            // Random delay to prevent predictable request patterns
            await Task.Delay(new Random().Next(_delayBetweenRequestsMs, _delayBetweenRequestsMs * 2));

            return await requestAction();
        }
        catch (Exception ex)
        {
            Log.Error($"Request failed: {ex.Message}");
            throw;
        }
        finally
        {
            _rateLimiter.Release();
        }
    }

    public async Task ProcessAsync()
    {
        try
        {
            await FetchAndProcessDataForRange();
        }
        catch (Exception ex)
        {
            Log.Error($"Error processing data: {ex.Message}");
            throw;
        }
    }


    //Fetch and process data for range
    private async Task FetchAndProcessDataForRange()
    {
        var searchResults = await Search();
        var outputFileName = $"thong_bao_moi_thau_{DateTime.Now:yyyyMMdd_HHmmss}.json";
        var errorFileName = $"errors_{DateTime.Now:yyyyMMdd_HHmmss}.json";

        // Create output directories
        var outputPath = Path.Combine(Directory.GetCurrentDirectory(), OutputFolder, outputFileName);
        var errorPath = Path.Combine(Directory.GetCurrentDirectory(), ErrorOutputFolder, errorFileName);

        await CheckFolderAsync(Path.GetDirectoryName(outputPath));
        await CheckFolderAsync(Path.GetDirectoryName(errorPath));

        // Ensure files exist
        await using (File.Create(outputPath))
        {
        }

        await using (File.Create(errorPath))
        {
        }

        // Use a channel for incremental saving
        var channelOptions = new BoundedChannelOptions(10)
        {
            FullMode = BoundedChannelFullMode.Wait
        };
        var dataChannel = Channel.CreateBounded<object>(channelOptions);
        var errorChannel = Channel.CreateBounded<object>(channelOptions);

        // Start incremental saving tasks
        var dataSaveTask = IncrementalSaveAsync(dataChannel.Reader, outputPath);
        var errorSaveTask = IncrementalSaveAsync(errorChannel.Reader, errorPath);

        // Process search results
        await Parallel.ForEachAsync(
            searchResults,
            new ParallelOptions { MaxDegreeOfParallelism = _maxConcurrentTasks },
            async (searchResult, token) =>
            {
                var listDetailByVersion = new List<object>();
                var processResult = new
                {
                    SearchResult = searchResult,
                    Errors = new List<string>()
                };

                try
                {
                    var lstVersions = GetVersionsToFetch(searchResult.Version!);

                    // Parallel processing of versions
                    var versionTasks = lstVersions.Select(async version =>
                    {
                        try
                        {
                            Log.Information($"Processing version {version} for {searchResult.Code}");
                            var detail = await GetDetailAsync(searchResult.Code!, version);

                            return new
                            {
                                detail
                            };
                        }
                        catch (Exception ex)
                        {
                            processResult.Errors.Add($"Error processing version {version}: {ex.Message}");
                            return null;
                        }
                    });

                    var versionResults = await Task.WhenAll(versionTasks);
                    listDetailByVersion.AddRange(versionResults.Where(r => r != null)!);
                }
                catch (Exception ex)
                {
                    processResult.Errors.Add($"Error processing search result: {ex.Message}");
                }

                var objectJsonSave = new
                {
                    thongTinLayTuDanhSachTimKiem = searchResult,
                    detailByVersion = listDetailByVersion
                };

                // If there are any errors, save to error file
                if (processResult.Errors.Any())
                {
                    var errorObject = new
                    {
                        processResult.SearchResult,
                        Errors = processResult.Errors
                    };
                    await errorChannel.Writer.WriteAsync(errorObject, token);
                }

                // Save successful results
                await dataChannel.Writer.WriteAsync(objectJsonSave, token);
            });

        // Signal completion of writing
        dataChannel.Writer.Complete();
        errorChannel.Writer.Complete();

        // Wait for saving to complete
        await Task.WhenAll(dataSaveTask, errorSaveTask);
    }

    private async Task IncrementalSaveAsync(
        ChannelReader<object> channelReader,
        string filePath)
    {
        await using var fileStream = new FileStream(filePath, FileMode.Append, FileAccess.Write, FileShare.Read);
        await using var writer = new StreamWriter(fileStream);
        await using var jsonWriter = new JsonTextWriter(writer)
        {
            Formatting = Formatting.Indented
        };

        // Initialize the array
        await writer.WriteAsync("[\n");
        bool isFirst = true;

        try
        {
            await foreach (var item in channelReader.ReadAllAsync())
            {
                if (!isFirst)
                {
                    await writer.WriteAsync(",\n");
                }
                else
                {
                    isFirst = false;
                }

                // Serialize and write each item
                var serializer = new JsonSerializer();
                serializer.Serialize(jsonWriter, item);
                await jsonWriter.FlushAsync();
            }
        }
        catch (Exception ex)
        {
            Log.Error($"Error during incremental saving to {filePath}: {ex.Message}");
            throw;
        }
        finally
        {
            // Close the array
            await writer.WriteAsync("\n]");
        }
    }


    //Setup logging
    private static void SetupLogging()
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .CreateLogger();
    }

    //Check folder
    private static Task CheckFolderAsync(string? path)
    {
        return Task.Run(() =>
        {
            if (Directory.Exists(path)) return;
            Log.Information($"Creating folder {path}");
            Directory.CreateDirectory(path ?? throw new ArgumentNullException(nameof(path)));
        });
    }

    //Create request
    private RestRequest CreateRestRequest(string url)
    {
        var restRequest = new RestRequest(url)
        {
            Method = Method.Post,
            RequestFormat = DataFormat.Json,
            Timeout = new TimeSpan(0, 0, 30),
        };

        var payLoad = new BodyPayloadCommon
        {
            Body =
            [
                new()
                {
                    PageSize = MaxRecords,
                    PageNumber = 0,
                    Query =
                    [
                        new()
                        {
                            Index = "es-ebid-search",
                            KeyWord = "",
                            MatchType = "all-1",
                            MatchFields =
                            [
                                "code",
                                "name"
                            ],
                            Filters =
                            [
                                new()
                                {
                                    FieldName = "type", SearchType = "in", FieldValues = ["dsn"]
                                },

                                new()
                                {
                                    FieldName = "linh_vuc",
                                    SearchType = "in",
                                    FieldValues =
                                    [
                                        (int)FieldBiddingLaw.HangHoa,
                                        (int)FieldBiddingLaw.XayLap,
                                        (int)FieldBiddingLaw.TuVan,
                                        (int)FieldBiddingLaw.PhiTuVan,
                                        (int)FieldBiddingLaw.HonHop
                                    ]
                                },
                            ]
                        }
                    ]
                }
            ]
        };

        restRequest.AddJsonBody(payLoad);
        return restRequest;
    }

    //Search
    private async Task<List<DanhSachNganSearch>> Search()
    {
        return await ExecuteRateLimitedRequestAsync(async () =>
        {
            try
            {
                var restRequest = CreateRestRequest(UrlOrigin);
                var response =
                    await _restClient.ExecuteAsync<ResponseModel<DanhSachNganSearch>>(restRequest);

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    return response.Data.Body.Page.Content;
                }

                Log.Warning($"Error searching data: {response.ErrorMessage}");
                return new List<DanhSachNganSearch>();
            }
            catch (Exception e)
            {
                Log.Error($"Error searching data: {e.Message}");
                return new List<DanhSachNganSearch>();
            }
        });
    }

    //get detail
    // https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bid-shortlist/get-detail
    // {"body":{"bidNo":"20221004020","bidTurnNo":"00"}}

    private async Task<DanhSachNganDetail?> GetDetailAsync(string bidNo, string version)
    {
        return await ExecuteRateLimitedRequestAsync(async () =>
        {
            try
            {
                var url = "https://muasamcong.mpi.gov.vn/api/unau/oldsearchpo/bid-shortlist/get-detail";
                var restRequest = new RestRequest(url)
                {
                    Method = Method.Post,
                    RequestFormat = DataFormat.Json,
                    Timeout = new TimeSpan(0, 0, 30),
                };

                var payLoad = new
                {
                    body = new
                    {
                        bidNo = bidNo,
                        bidTurnNo = version
                    }
                };

                restRequest.AddJsonBody(payLoad);
                var response = await _restClient.ExecuteAsync<ApiResponse<DanhSachNganDetail>>(restRequest);

                if (response.IsSuccessful)
                {
                    return response.Data!.Body;
                }

                Log.Warning($"Error fetching detail for {bidNo} - {version}: {response.ErrorMessage}");
                return new DanhSachNganDetail();
            }
            catch (Exception e)
            {
                Log.Error($"Error fetching detail for {bidNo} - {version}: {e.Message}");
                return new DanhSachNganDetail();
            }
        });
    }


    // Get versions to fetch
    private List<string> GetVersionsToFetch(string currentVersion)
    {
        var versions = new List<string> { currentVersion };
        int versionNumber = int.Parse(currentVersion);

        while (versionNumber > 0)
        {
            Log.Information($"Adding version {versionNumber}");
            versionNumber--;
            versions.Add(versionNumber.ToString("D2"));
        }

        return versions;
    }
}