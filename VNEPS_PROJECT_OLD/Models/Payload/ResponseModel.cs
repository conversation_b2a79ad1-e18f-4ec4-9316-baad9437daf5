namespace VNEPS_PROJECT_OLD.Models.Payload;

public class ResponseModel<T>
{
    public string? ResponseCode { get; set; }
    public string? ResponseMessage { get; set; }
    public ResponseBody<T>? Body { get; set; }
}

public class ResponseBody<T>
{
    public Page<T>? Page { get; set; }
    public List<Agg>? Agg { get; set; }
}

public class Page<T>
{
    public List<T>? Content { get; set; }
    public int TotalPages { get; set; }
    public int PageSize { get; set; }
    public int CurrentPage { get; set; }
    public int TotalElements { get; set; }
    public bool Last { get; set; }
    public bool First { get; set; }
    public bool Empty { get; set; }
}

public class Content
{
    public string? Id { get; set; }
    public string? Code { get; set; }
    public string? Version { get; set; }
    public string? Type { get; set; }
    public string? Name { get; set; }
    public string? NgayDangTai { get; set; }
    public string? NgayPheDuyet { get; set; }
    public string? TenChuDauTu { get; set; }
    public double TongMucDauTu { get; set; }
    public string? InputUserCd { get; set; }
    public string? ProjectGroup { get; set; }
}

public class ContentPlan
{
    public string Id { get; set; }
    public string Code { get; set; }
    public string Version { get; set; }
    public string IdKeHoach { get; set; }
    public string Type { get; set; }
    public string MaBenMoiThau { get; set; }
    public string Name { get; set; }
    public string NgayDangTai { get; set; }
    public string NgayPheDuyet { get; set; }
    public string TenBenMoiThau { get; set; }
    public string TenChuDauTu { get; set; }
    public string TongMucDauTu { get; set; }
    public string InputUserCd { get; set; }
}

public class Agg
{
    public string? Name { get; set; }
    public Metadata? Metadata { get; set; }
    public List<Bucket>? Buckets { get; set; }
    public string? Type { get; set; }
    public bool Fragment { get; set; }
}

public class Metadata
{
    public Dictionary<string, object> AsMap { get; set; }
}

public class Bucket
{
    public Aggregations? Aggregations { get; set; }
    public string? KeyAsString { get; set; }
    public int DocCount { get; set; }
    public string? Key { get; set; }
    public bool Fragment { get; set; }
}

public class Aggregations
{
    public Dictionary<string, object>? AsMap { get; set; }
    public bool Fragment { get; set; }
}