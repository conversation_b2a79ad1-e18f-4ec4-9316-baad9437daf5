namespace VNEPS_PROJECT_OLD.Models.Payload;

public class BodyPayloadCommon
{
    public List<Body>? Body { get; set; }
}

public class Body
{
    public int PageSize { get; set; }
    public int PageNumber { get; set; }
    public List<Query>? Query { get; set; }
}

public class Query
{
    public string? Index { get; set; }
    public string? KeyWord { get; set; }
    public string? MatchType { get; set; }
    public List<string>? MatchFields { get; set; }
    public List<Filter>? Filters { get; set; }
}

public class Filter
{
    public string? FieldName { get; set; }
    public string? SearchType { get; set; }
    public List<dynamic>? FieldValues { get; set; }
    public string? From { get; set; }
    public string? To { get; set; }
}