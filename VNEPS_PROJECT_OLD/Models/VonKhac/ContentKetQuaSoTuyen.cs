namespace VNEPS_PROJECT_OLD.Models.VonKhac;

public class ContentKetQuaSoTuyen
{
    public string? Id { get; set; }
    public string? Code { get; set; }
    public string? Version { get; set; }
    public string? Type { get; set; }
    public string? Name { get; set; }
    public DateTime? NgayDangTai { get; set; }
    public DateTime? NgayMoThau { get; set; }
    public string? TenBenMoiThau { get; set; }
    public string? TenGoiThau { get; set; }
    public string? BidType { get; set; }
    public string? InputUserCd { get; set; }
}

public class ResultPreQualifiedVkOfDetailDTO
{
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? BidType { get; set; }
    public string? SubBidType { get; set; }
    public string? BidSuccmethod { get; set; }
    public string? RegYn { get; set; }
    public string? PublicDt { get; set; }
    public string? PublicYn { get; set; }
    public string? InputUserCd { get; set; }
    public string? BidNm { get; set; }
    public string? BidInfo { get; set; }
    public string? ProjectName { get; set; }
    public string? BidInstituname { get; set; }
    public string? BidMethod { get; set; }
    public string? BidOpenLocation { get; set; }
    public string? BidArea { get; set; }
    public string? BidTypeDetail { get; set; }
    public string? BidStartDt { get; set; }
    public string? BidEndDt { get; set; }
    public string? BidCapital { get; set; }
    public string? RefNo { get; set; }
    public string? KhBidNo { get; set; }
    public string? FundingType { get; set; }
    public string? FundingNote { get; set; }
    public string? CdNm { get; set; }
    public string? CancelYn { get; set; }
    public string? PqResultDt { get; set; }
    public string? BidOpenDt { get; set; }
    public string? EbidMethod { get; set; }
}

public class BodyResultPreQualifiedVkOfDetailDTO
{
    public ResultPreQualifiedVkOfDetailDTO? ResultPreQualifiedVkOfDetailDTO { get; set; }
}

public class ContractorVkOfListDTO
{
    public string? BizNm { get; set; }
    public string? BidAddress { get; set; }
    public string? BidLocation { get; set; }
    public string? BizRegNo { get; set; }
    public string? BizNmEn { get; set; }
    public string? BidAddressEn { get; set; }
    public string? BidLocationEn { get; set; }
}

public class BodyContractorVkOfListDTO
{
    public List<ContractorVkOfListDTO> ContractorVkOfListDTOList { get; set; }
    public int TotalElement { get; set; }
}