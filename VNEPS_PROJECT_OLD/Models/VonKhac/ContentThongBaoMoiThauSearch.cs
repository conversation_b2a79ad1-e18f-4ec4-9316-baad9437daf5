namespace VNEPS_PROJECT_OLD.Models.VonKhac;

public class ContentThongBaoMoiThauSearch
{
    public string? Id { get; set; }
    public string? Code { get; set; }
    public string? Version { get; set; }
    public string? Type { get; set; }
    public string? CancelYn { get; set; }
    public string? HinhThucDuThau { get; set; }
    public string? MaBenMoiThau { get; set; }
    public DateTime? NgayDangTai { get; set; }
    public DateTime? NgayDongThau { get; set; }
    public string? TenBenMoiThau { get; set; }
    public string? TenGoiThau { get; set; }
    public string? KhBidNo { get; set; }
    public string? BidType { get; set; }
    public string? Sponsor { get; set; }
    public DateTime? BidEndDtMas { get; set; }
    public string? InputUserCd { get; set; }
    public string? QuyTrinhApDung { get; set; }
    public string? EbidMethod { get; set; }
}

public class BidDetailResponse
{
    public DetailDTO? DetailDTO { get; set; }
    public object? DataEntryADBXLDTOS { get; set; }
    public object? Bdl { get; set; }
    public object? FormModel { get; set; }
    public object? FormModel2 { get; set; }
    public object? FormModel3 { get; set; }
    public object? MeasureMethod { get; set; }
    public object? HangMucXL { get; set; }
    public object? BangKeCongNhat { get; set; }
    public object? BangCacKhoanTT { get; set; }
    public object? BangSoLieuDC { get; set; }
    public object? BangTienLuongTHDTOS { get; set; }
    public object? QuocGiaHopLes { get; set; }
    public object? EntityUtil { get; set; }
    public object? DieuKienCuTheVKDTOS { get; set; }
    public object? DanhMucHangHoaDTOS { get; set; }
    public object? DanhMucDichVuDTOS { get; set; }
    public object? NotifyNotInternetDTOS { get; set; }
}

public class DetailDTOThongBaoMOiThau
{
    public string? BidPlanItemMethodEn { get; set; }
    public string? BidSecuPriceEn { get; set; }
    public string? BidSecuMethodEn { get; set; }
    public string? BidContractTimeEn { get; set; }
    public string? BidDescriptionEn { get; set; }
    public string? BidEstimatedpriceEn { get; set; }
    public string? BidEstimatedpriceEnCurrency { get; set; }
    public string? BidSecuPriceEnCurrency { get; set; }
    public string? OrderInstituCdName { get; set; }
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? RegYn { get; set; }
    public string? BidType { get; set; }
    public string? BidInstituCd { get; set; }
    public string? BidInstituFullNm { get; set; }
    public string? OrderInstituCd { get; set; }
    public string? BidOrderInstituname { get; set; }
    public string? BidNm { get; set; }
    public string? ProjectName { get; set; }
    public string? Capital { get; set; }
    public string? RefNo { get; set; }
    public string? ContrMethod { get; set; }
    public string? SuccBidMethod { get; set; }
    public string? PqYn { get; set; }
    public string? InterBidYn { get; set; }
    public string? BidMethod { get; set; }
    public string? BidStartDt { get; set; }
    public string? BidStartTime { get; set; }
    public string? BidEndDt { get; set; }
    public string? BidEndTime { get; set; }
    public string? BidAttendCostPayYn { get; set; }
    public string? BidAttendCost { get; set; }
    public string? BidAttendCostBankCd { get; set; }
    public string? BidAttendAcctNo { get; set; }
    public string? BidAttendAccNm { get; set; }
    public string? BidSellLoca { get; set; }
    public string? BidSubLoca { get; set; }
    public string? DateSubmitBidSecu { get; set; }
    public string? TimeSubmitBidSecu { get; set; }
    public string? BidSecuSubFlg { get; set; }
    public string? BankCdBidSecu { get; set; }
    public string? AccNoBidSecu { get; set; }
    public string? AccNmBidSecu { get; set; }
    public string? PlaceSubmitBidSecu { get; set; }
    public string? JointappRecvDt { get; set; }
    public string? JointappRecvTime { get; set; }
    public string? JointappRecvYn { get; set; }
    public string? JointMethod { get; set; }
    public string? BidOpenDt { get; set; }
    public string? BidOpenTime { get; set; }
    public string? BidOpenPlace { get; set; }
    public string? AttendLimitArea1 { get; set; }
    public string? AttendLimitArea2 { get; set; }
    public string? AttendLimitArea3 { get; set; }
    public string? LimitLicenseCd1 { get; set; }
    public string? LimitLicenseCd2 { get; set; }
    public string? LimitLicenseCd3 { get; set; }
    public string? LimitLicenseCd4 { get; set; }
    public string? LimitLicenseCd5 { get; set; }
    public string? LimitLicenseCd6 { get; set; }
    public string? LimitLicenseCd7 { get; set; }
    public string? LimitLicenseCd8 { get; set; }
    public string? LimitLicenseCd9 { get; set; }
    public string? LimitLicenseCd10 { get; set; }
    public string? LimitLicenseCd11 { get; set; }
    public string? LimitLicenseCd12 { get; set; }
    public string? InputUserCd { get; set; }
    public string? OrgBidNo { get; set; }
    public string? CancelYn { get; set; }
    public string? PublicYn { get; set; }
    public string? PublicDt { get; set; }
    public string? SysDate { get; set; }
    public string? ApproveState { get; set; }
    public string? ExchangeRefDt { get; set; }
    public string? ExchangeRefTime { get; set; }
    public string? ExchangeRefBankCd { get; set; }
    public string? ExchangeToCurrCd { get; set; }
    public string? EstiPrice { get; set; }
    public string? SysStatus { get; set; }
    public string? BidAttendCostOther { get; set; }
    public string? BidTypeDetail { get; set; }
    public string? BidNmEn { get; set; }
    public string? BidCapitalEn { get; set; }
    public string? BidSublocaEn { get; set; }
    public string? BidInstitunameEn { get; set; }
    public string? BidProjectnameEn { get; set; }
    public string? BidArea { get; set; }
    public string? BidDescription { get; set; }
    public string? BidContractTime { get; set; }
    public string? BidSecuMethod { get; set; }
    public string? BidSecuPrice { get; set; }
    public string? BidPlanItemMethod { get; set; }
    public string? InstituPhoneNo { get; set; }
    public string? Mobile { get; set; }
    public string? BidPlanProjectStyle { get; set; }
    public string? BidNotStock { get; set; }
    public string? BidPlanNm { get; set; }
    public string? KhBidNo { get; set; }
    public string? Sponsor { get; set; }
    public string? ContractDaysStyle { get; set; }
    public string? EffectTime { get; set; }
    public string? Totalinvesmtnet1 { get; set; }
    public string? AddressShopQm { get; set; }
    public string? Totalinvesbmnet1 { get; set; }
    public string? Totalinvesbmnet2 { get; set; }
    public string? Totalinvesbmnet3 { get; set; }
    public string? SupplyCapitalBm { get; set; }
    public string? EndowSolution { get; set; }
    public string? CapitalType { get; set; }
    public string? EnsureBidMethod { get; set; }
    public string? PrePriceMethod { get; set; }
}

public class BidTechnicalResponse
{
    public BidTechRuleDTO? BidTechRuleDTO { get; set; }
    public ThietBiThiCongVKDTO? ThietBiThiCongVKDTO { get; set; }
}

public class BidTechRuleDTO
{
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? BidType { get; set; }
    public string? TechniqueRegulation { get; set; }
    public string? Drawing { get; set; }
    public string? AdditionInfo { get; set; }
    public string? Eshs { get; set; }
}

public class ThietBiThiCongVKDTO
{
    public string? Id { get; set; }
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? BidType { get; set; }
    public string? BidThietBi { get; set; }
    public string? BidDacDiem { get; set; }
    public string? BidSoLuong { get; set; }
}

public class BidFileResponse
{
    public List<BidFile>? ListFile { get; set; }
}

public class BidFile
{
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? SerialNo { get; set; }
    public string? FileType { get; set; }
    public string? BidStdFile { get; set; }
}

public class BidNotifyYclr
{
    public List<object> ListYclr { get; set; }
}

//meetingDTOs
public class MeetingDTOs
{
    public List<object>? MeetingDTO { get; set; }
}

public class BidCancelResponse
{
    public CancelDTOThongBaoMoiThau? CancelDTO { get; set; }
}

public class CancelDTOThongBaoMoiThau
{
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? CancelReason { get; set; }
    public string? CancelStep { get; set; }
    public string? FileName { get; set; }
    public string? InputUserCd { get; set; }
    public string? CancelRefNo { get; set; }
    public string? ApproveDate { get; set; }
    public string? InputDate { get; set; }
    public string? BidMethod { get; set; }
    public string? FileServerName { get; set; }
    public string? BidNm { get; set; }
    public string? BidEndDt { get; set; }
    public string? ExecInfo { get; set; }
    public string? FileName1 { get; set; }
    public string? FileServerName1 { get; set; }
}