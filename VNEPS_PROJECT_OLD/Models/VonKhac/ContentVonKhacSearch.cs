namespace VNEPS_PROJECT_OLD.Models.VonKhac;

public class ContentVonKhacSearch
{
    public Guid Id { get; set; }
    public string? Code { get; set; }
    public string? Version { get; set; }
    public string? Type { get; set; }
    public string? Name { get; set; }
    public string? NgayDangTai { get; set; }
    public string? TenChuDauTu { get; set; }
    public decimal? BidPlanProjectPriceVnd { get; set; }
    public decimal? BidPlanProjectPriceUsd { get; set; }
    public decimal? BidPlanProjectPriceEur { get; set; }
    public string? BidPlanProjectId { get; set; }
    public string? InputUserCd { get; set; }
}

public class BidPlanProjectVKOfDetailDTO
{
    public string? BidInstitunameEn { get; set; }
    public string? BidPlanProjectNm { get; set; }
    public string? BidPlanProjectInvestNm { get; set; }
    public string? BidPlanProjectStyle { get; set; }
    public string? BidPlanProjectCompanyIssue { get; set; }
    public string? BidPlanProjectRefnum { get; set; }
    public string? BidPlanProjectDateIssue { get; set; }
    public string? BidPlanProjectPriceVnd { get; set; }
    public string? BidPlanProjectPriceUsd { get; set; }
    public string? BidPlanProjectPriceEur { get; set; }
    public string? BidPlanProjectType { get; set; }
    public string? BidPlanProjectFund { get; set; }
    public string? BidPlanProjectClassify { get; set; }
    public string? BidPlanProjectPlace { get; set; }
    public string? BidPlanProjectDescription { get; set; }
    public string? BidPlanProjectCompApprove { get; set; }
    public string? BidPlanProjectRefApprove { get; set; }
    public string? BidPlanProjectDateApprove { get; set; }
    public string? BidPlanNm { get; set; }
    public string? BidPlanProjectStdClsCd { get; set; }
    public string? CdNm { get; set; }
    public string? RegYn { get; set; }
    public string? BidPlanProjectPriceOther { get; set; }
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? PublicYn { get; set; }
    public string? Determined { get; set; }
    public string? PublicDt { get; set; }
}

public class ContentBidPlanProjectVKOfDetailDTO
{
    public BidPlanProjectVKOfDetailDTO? bidPlanProjectVKOfDetailDTO { get; set; }
}

public class BidPlanBidVkInfoDTO
{
    public string? Rnum { get; set; }
    public string? BidPlanItemNm { get; set; }
    public string? BidPlanItemRefNum { get; set; }
    public string? BidPlanItemStyle { get; set; }
    public string? BidPlanItemFund { get; set; }
    public string? BidPlanProjectPriceVnd { get; set; }
    public string? BidPlanProjectPriceUsd { get; set; }
    public string? BidPlanProjectPriceEur { get; set; }
    public string? BidPlanItemPrice { get; set; }
    public string? BidPlanItemMethodSelect { get; set; }
    public string? BidPlanItemIsinternational { get; set; }
    public string? BidPlanItemIspq { get; set; }
    public string? BidPlanItemMethod { get; set; }
    public string? BidPlanItemClassify { get; set; }
    public string? BidPlanTime { get; set; }
    public string? ContractStyle { get; set; }
    public string? ContractDays { get; set; }
    public string? BidPlanItemDetailsId { get; set; }
    public string? BidPlanProjectPriceOther { get; set; }
    public string? DatetimeBid { get; set; }
    public string? BidType { get; set; }
    public string? BidPlanItemInternet { get; set; }
    public string? BidPlanFilename { get; set; }
}
public class BidPlanBidVkInfoDTOList
{
    public List<BidPlanBidVkInfoDTO> bidPlanBidVkInfoDTOList { get; set; }
}