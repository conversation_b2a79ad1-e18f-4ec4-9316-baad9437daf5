namespace VNEPS_PROJECT_OLD.Models.VonKhac;

public class ContentThongBaoMoiThauSearchSoTuyen
{
    public string? Id { get; set; }
    public string? Code { get; set; }
    public string? Version { get; set; }
    public string? Type { get; set; }
    public string? TrangThai { get; set; }
    public string? MaBenMoiThau { get; set; }
    public string? Name { get; set; }
    public string? NgayDangTai { get; set; }
    public string? NgayDongThau { get; set; }
    public string? TenBenMoiThau { get; set; }
    public string? TenGoiThau { get; set; }
    public string? InputUserCd { get; set; }
}

public class CancelDTO
{
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? CancelReason { get; set; }
    public string? CancelStep { get; set; }
    public string? FileName { get; set; }
    public string? InputUserCd { get; set; }
    public string? CancelRefNo { get; set; }
    public string? ApproveDate { get; set; }
    public string? InputDate { get; set; }
    public string? BidMethod { get; set; }
    public string? FileServerName { get; set; }
    public string? BidNm { get; set; }
    public string? BidEndDt { get; set; }
    public string? ExecInfo { get; set; }
    public string? FileName1 { get; set; }
    public string? FileServerName1 { get; set; }
}
public class ThongBaoHuyThauMoiThau
{
    public CancelDTO? cancelDTO { get; set; }
}

public class DetailDTO
{
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? BidType { get; set; }
    public string? SubBidType { get; set; }
    public string? BidSuccMethod { get; set; }
    public string? RegYn { get; set; }
    public string? PublicDt { get; set; }
    public string? PublicYn { get; set; }
    public string? InputUserCd { get; set; }
    public string? BidNm { get; set; }
    public string? BidInfo { get; set; }
    public string? ProjectName { get; set; }
    public string? BidInstituName { get; set; }
    public string? BidMethod { get; set; }
    public string? BidOpenLocation { get; set; }
    public string? BidArea { get; set; }
    public string? BidTypeDetail { get; set; }
    public string? BidStartDt { get; set; }
    public string? BidEndDt { get; set; }
    public string? BidCapital { get; set; }
    public string? FundingType { get; set; }
    public string? CancelYn { get; set; }
    public string? StatusHandling { get; set; }
    public string? BidQuick { get; set; }
    public string? Notice { get; set; }
    public string? BidOpenDt { get; set; }
    public string? BidCloseDt { get; set; }
    public string? SubBidTypeNm { get; set; }
    public string? BidSuccMethodNm { get; set; }
    public string? RefNo { get; set; }
    public string? KhBidNo { get; set; }
    public string? BidPlanItemRefNum { get; set; }
    public string? FundingNote { get; set; }
    public string? EbidMethod { get; set; }
}

public class ThongBaoMoiThauChiTiet
{
    public DetailDTO? detailDTO { get; set; }
}

public class FileInfo
{
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? SerialNo { get; set; }
    public string? FileType { get; set; }
    public string? BidStdFile { get; set; }
}

public class FileList
{
    public List<FileInfo>? ListFile { get; set; }
}