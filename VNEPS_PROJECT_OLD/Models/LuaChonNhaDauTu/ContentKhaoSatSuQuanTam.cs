namespace VNEPS_PROJECT_OLD.Models.LuaChonNhaDauTu;

public class ContentKhaoSatSuQuanTam
{
    public string? Id { get; set; } // ID của dự án
    public string? Code { get; set; } // Mã dự án
    public string? Version { get; set; } // Phiên bản
    public string? Type { get; set; } // Loại dự án
    public string? Name { get; set; } // Tên dự án
    public DateTime? NgayDangTai { get; set; } // Ngày đăng tải
    public DateTime? NgayHetHanDangKy { get; set; } // Ngày hết hạn đăng ký
    public string? InputUserCd { get; set; } // Mã người dùng nhập liệu
    public string? DonViDangTai { get; set; } // Đơn vị đăng tải
    public string? FormType { get; set; } // Loại biểu mẫu
}

public class DetailSurvey
{
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? BidInstituname { get; set; }
    public string? CancelYn { get; set; }
    public string? RegYn { get; set; }
    public string? ProjectName { get; set; }
    public string? ProjectPurpose { get; set; }
    public string? ProjectLocation { get; set; }
    public string? ProjectTime { get; set; }
    public string? ProjectAreaUsed { get; set; }
    public string? ProjectType { get; set; }
    public string? ProjectTotalInvest { get; set; }
    public string? ProjectGuaranteed { get; set; }
    public string? ProjectSharingMechanism { get; set; }
    public string? ProjectInvestor { get; set; }
    public string? ProjectPreparationUnit { get; set; }
    public string? ProjectRule { get; set; }
    public string? ProjectDeployCapabilities { get; set; }
    public string? ProjectSurveyRequired { get; set; }
    public string? ProjectEvaluate { get; set; }
    public string? OtherInfo { get; set; }
    public string? DeadlineRegistDt { get; set; }
    public string? ProjectInfo { get; set; }
    public string? ProjectSpecialFactors { get; set; }
    public string? ProjectEquity { get; set; }
    public string? ProjectMoneyType { get; set; }
    public string? ProjectUsed { get; set; }
    public string? ProjectStatus { get; set; }
    public string? ProjectPlan { get; set; }
    public string? ProjectDetail { get; set; }
    public string? PublicDt { get; set; }
    public string? InputUserCd { get; set; }
    public string? StartRegisterDt { get; set; }
    public string? EndRegisterDt { get; set; }
    public string? Filename { get; set; }
    public string? ExperienceRequireFile { get; set; }
    public string? FormType { get; set; }
    public string? BidMethod { get; set; }
    public string? BidType { get; set; }
    public string? PublicYn { get; set; }
    public string? InputDt { get; set; }
    public string? SpecialFactors { get; set; }
    public string? ProjectNameEn { get; set; }
    public string? ProjectPurposeEn { get; set; }
    public string? ProjectLocationEn { get; set; }
    public string? ProjectTimeEn { get; set; }
    public string? ProjectAreaUsedEn { get; set; }
    public string? ProjectTypeEn { get; set; }
    public string? ProjectTotalInvestEn { get; set; }
    public string? ProjectGuaranteedEn { get; set; }
    public string? ProjectSharingMechanismEn { get; set; }
    public string? ProjectInvestorEn { get; set; }
    public string? ProjectPreparationUnitEn { get; set; }
    public string? ProjectDeployCapabilitiesEn { get; set; }
    public string? ProjectSurveyRequiredEn { get; set; }
    public string? ProjectEvaluateEn { get; set; }
    public string? OtherInfoEn { get; set; }
    public string? DeadlineRegistDtEn { get; set; }
    public string? ProjectInfoEn { get; set; }
    public string? ProjectMoneyTypeEn { get; set; }
    public string? MininumEquity { get; set; }
    public string? MoneyTypeEquity { get; set; }
    public string? NumberProject { get; set; }
    public string? ProjectSpecialFactorsEn { get; set; }
    public string? MininumEquityEn { get; set; }
    public string? MoneyTypeEquityEn { get; set; }
    public string? NumberProjectEn { get; set; }
    public string? NoticeFile { get; set; }
    public string? CompetentAuthority { get; set; }
    public string? CompetentAuthorityEn { get; set; }
    public string? TypeLanguage { get; set; }
    public string? TypeLanguageEn { get; set; }
    public string? InstructionFile { get; set; }
    public string? FinancialPreliminary { get; set; }
    public string? FinancialPreliminaryEn { get; set; }
}

public class BodyKhaoSatSuQuanTam
{
    public DetailSurvey DetailSurvey { get; set; }
    public object? NotiInfo { get; set; }
    public object? NotiInfoHis { get; set; }
    public List<object>? ClarifyDTOList { get; set; }
}