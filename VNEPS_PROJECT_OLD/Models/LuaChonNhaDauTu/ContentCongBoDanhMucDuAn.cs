namespace VNEPS_PROJECT_OLD.Models.LuaChonNhaDauTu;

public class ContentCongBoDanhMucDuAn
{
    public Guid? Id { get; set; } // Nullable Guid
    public string? Code { get; set; } // Nullable string?

    public string? Version { get; set; } // Nullable string?

    public string? Type { get; set; } // Nullable string?

    public string? TrangThai { get; set; } // Nullable string?

    public string? HinhThucDuThau { get; set; } // Nullable string?

    public string? Name { get; set; } // Nullable string?

    public DateTime? NgayDangTai { get; set; } // Nullable DateTime
    public string? LoaiThongBao { get; set; } // Nullable string?

    public string? InputUserCd { get; set; } // Nullable string?

    public string? DonViDangTai { get; set; } // Nullable string?

    public string? ZipCd { get; set; } // Nullable string?

    public string? CdNm { get; set; } // Nullable string?

    public DateTime? DeadlineRegistDt { get; set; } // Nullable DateTime
}

public class BodyGetExtend
{
    public List<object>? ExtendDTO { get; set; } // Nullable list
}

public class BodyCongBoDanhMucDuAN
{
    public List<object>? ListTBKSDTOS { get; set; } // Nullable list
    public object TotalElement { get; set; } // Nullable
    public List<object>? ListInvestorBefore { get; set; } // Nullable list
    public object CntBefore { get; set; } // Nullable
    public List<object>? ListInvestorAfter { get; set; } // Nullable list
    public object CntAfter { get; set; } // Nullable
    public List<object>? DetailTBKSQTDTOS { get; set; } // Nullable list
    public List<object>? SearchYCLRNLKNDTOS { get; set; } // Nullable list
    public List<object>? DetailYCLRNLKNDTOS { get; set; } // Nullable list
    public List<object>? ListTuLRHSDKDTOS { get; set; } // Nullable list
    public List<object>? DetailTuLRHSDKaDTOS { get; set; } // Nullable list
    public List<object>? DetailTuLRHSDKbDTOS { get; set; } // Nullable list
    public List<object>? ListRutThauSDDDTOS { get; set; } // Nullable list
    public List<object>? DetailRutThauSDDDTOS { get; set; } // Nullable list
    public List<CbdmdaDetailDTO>? CbdmdaDetailDTOS { get; set; } // List of CbdmdaDetailDTO objects
    public List<object>? DetailLamRoLcndtDTOS { get; set; } // Nullable list
    public List<object>? DetailEvaluationLcndtDTOS { get; set; } // Nullable list
    public object? ResultNLKNDTOS { get; set; } // Nullable
    public List<object>? ListContractorSddDTOS { get; set; } // Nullable list
    public List<object>? CancelCorrectionDTOS { get; set; } // Nullable list
    public object? DetailContract { get; set; } // Nullable
}

public class CbdmdaDetailDTO
{
    public string? ProjectName { get; set; }
    public string? ProjectTotalInvest { get; set; }
    public string? ProjectTime { get; set; } // Nullable
    public string? ProjectLocation { get; set; }
    public string? ProjectUsed { get; set; }
    public string? ProjectStatus { get; set; }
    public string? ProjectPlan { get; set; } // Nullable
    public string? ProjectDetail { get; set; } // Nullable
    public string? ProjectInfo { get; set; }
    public string? PublicYn { get; set; }
    public string? ProjectPurpose { get; set; }
    public string? ProjectType { get; set; }
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? RegYn { get; set; }
    public string? CancelYn { get; set; }
    public string? BidInstituname { get; set; }
    public string? Filename { get; set; }
    public string? ProjectInvestGovernment { get; set; } // Nullable
    public string? ProjectMoneyType { get; set; }
    public string? BidPrepare { get; set; } // Nullable
    public string? ProjectNameEn { get; set; } // Nullable
    public string? ProjectTimeEn { get; set; } // Nullable
    public string? BidInstitunameEn { get; set; } // Nullable
    public string? ProjectTotalInvestEn { get; set; } // Nullable
    public string? ProjectMoneyTypeEn { get; set; }
    public string? ProjectInvestGovernmentEn { get; set; } // Nullable
    public string? ProjectPurposeEn { get; set; } // Nullable
    public string? ProjectLocationEn { get; set; } // Nullable
    public string? ProjectDetailEn { get; set; } // Nullable
    public string? ProjectStatusEn { get; set; } // Nullable
    public string? ProjectTypeEn { get; set; } // Nullable
    public string? InterBidYn { get; set; }
    public string? BidPrepareEn { get; set; } // Nullable
    public string? ProjectUsedEn { get; set; } // Nullable
    public string? ProjectScale { get; set; }
    public string? ProjectRequirement { get; set; }
    public string? InvestmentSchedule { get; set; }
    public string? LandUsesPurpose { get; set; }
    public string? ApprovedPlanning { get; set; }
    public string? OtherInfo { get; set; }
    public string? ProjectScaleEn { get; set; } // Nullable
    public string? ProjectRequirementEn { get; set; } // Nullable
    public string? InvestmentScheduleEn { get; set; } // Nullable
    public string? LandUsesPurposeEn { get; set; } // Nullable
    public string? ApprovedPlanningEn { get; set; } // Nullable
    public string? ProjectInfoEn { get; set; } // Nullable
    public string? OtherInfoEn { get; set; } // Nullable
    public string? BidMethod { get; set; }
    public string? ExperienceRequireFile { get; set; }
    public string? ExperienceRequireFileEn { get; set; }
    public string? DeadlineRegistDt { get; set; }
    public string? OtherFile { get; set; }
    public string? PublicDt { get; set; }
    public string? OpenYn { get; set; }
    public string? NoticeFile { get; set; }
}