namespace VNEPS_PROJECT_OLD.Models.LuaChonNhaDauTu;

public class ContentKetQuaSoTuyen
{
    public string? Id { get; set; }

    public string? Code { get; set; }

    public string? Version { get; set; }

    public string? Type { get; set; }

    public string? TrangThai { get; set; }

    public DateTime? NgayDangTai { get; set; }

    public DateTime? NgayMoThau { get; set; }

    public string? TenGoiThau { get; set; }

    public int? SoNT { get; set; }

    public string? LoaiThongBao { get; set; }

    public string? InputUserCd { get; set; }

    public string? InstituFullNm { get; set; }

    public string? InstituNm { get; set; }
}

public class BodyContractorVkOfListDTO
{
    public List<ContractorVkOfListDTO> ContractorVkOfListDTOList { get; set; }
}

public class ContractorVkOfListDTO
{
    public string? BizNm { get; set; }
    public string? BidAddress { get; set; }
    public string? BidLocation { get; set; }
    public string? BizRegNo { get; set; }
    public string? BizNmEn { get; set; }
    public string? BidAddressEn { get; set; }
    public string? BidLocationEn { get; set; }
}

public class BodyResultPreQualifiedLcndtOfDetailDTO
{
    public ResultPreQualifiedLcndtOfDetailDTO ResultPreQualifiedLcndtOfDetailDTO { get; set; }
}

public class ResultPreQualifiedLcndtOfDetailDTO
{
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? RefNo { get; set; }
    public string? InstituFullNm { get; set; }
    public string? PublicDt { get; set; }
    public string? PqNm { get; set; }
    public string? PqOpenDt { get; set; }
    public string? PqStartDt { get; set; }
    public string? PqEndDt { get; set; }
    public string? PqMethod { get; set; }
    public string? ProjectNm { get; set; }
    public string? BidFilename { get; set; }
    public string? OpinionEndDt { get; set; }
    public string? Notice { get; set; }
    public string? BidPqIneligibleRson { get; set; }
    public string? StatusHandling { get; set; }
    public string? InterPqYn { get; set; }
    public string? PqProjectnameEn { get; set; }
    public string? BidPqIneligibleRsonEn { get; set; }
    public string? InputDt { get; set; }
    public string? InstituEnNm { get; set; }
    public string? PublicYn { get; set; }
}