namespace VNEPS_PROJECT_OLD.Models.LuaChonNhaDauTu;

public class ContentKeHoachLuaChonNhaDauTu
{
    public Guid? Id { get; set; }

    public string? Code { get; set; }

    public string? Version { get; set; }

    public string? Type { get; set; }

    public string? Name { get; set; }

    public DateTime? NgayDangTai { get; set; }

    public string? TenBenMoiThau { get; set; }

    public string? LoaiThongBao { get; set; }

    public string? BidPlanProjectId { get; set; }

    public string? InputUserCd { get; set; }
}

public class DetailKeHoachLuaChonNhaDauTu
{
    public DetailDataKeHoachLuaChonNhaDauTu? Detail { get; set; }
}

public class DetailDataKeHoachLuaChonNhaDauTu
{
    public string? BidPlanProjectNm { get; set; }
    public string? BidPlanProjectInvestNm { get; set; }
    public string? BidPlanProjectStyle { get; set; }
    public string? BidPlanProjectCompanyIssue { get; set; }
    public string? BidPlanProjectRefnum { get; set; }
    public string? BidPlanProjectStartDate { get; set; }
    public string? BidPlanProjectPriceVnd { get; set; }
    public string? BidPlanProjectPriceUsd { get; set; }
    public string? BidPlanProjectPriceEur { get; set; }
    public string? BidPlanProjectType { get; set; }
    public string? BidPlanProjectFund { get; set; }
    public string? BidPlanProjectClassify { get; set; }
    public string? BidPlanProjectPlace { get; set; }
    public string? BidPlanProjectDescription { get; set; }
    public string? BidPlanProjectCompApprove { get; set; }
    public string? BidPlanProjectRefApprove { get; set; }
    public string? BidPlanProjectDateApprove { get; set; }
    public string? BidPlanNm { get; set; }
    public string? RegYn { get; set; }
    public string? InputUserCd { get; set; }
    public string? BidPlanProjectPriceOther { get; set; }
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? Determined { get; set; }
    public string? BidEstimatedCurrency { get; set; }
    public string? BidCapitalCurrency { get; set; }
    public string? BidFilename { get; set; }
    public string? BidInstitunameEn { get; set; }
    public string? BidPlanProjectNmEn { get; set; }
    public string? BidPlanProjectStartDateEn { get; set; }
    public string? BidPlanProjectPriceEn { get; set; }
    public string? BidEstimatedCurrencyEn { get; set; }
    public string? BidPlanProjectDescEn { get; set; }
    public string? BidPlanProPriceOtherEn { get; set; }
    public string? BidInteryn { get; set; }
    public string? BidPlanProjectId { get; set; }
    public string? CancelYn { get; set; }
    public DateTime? PublicDt { get; set; }
}