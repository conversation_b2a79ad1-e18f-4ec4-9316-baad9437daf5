namespace VNEPS_PROJECT_OLD.Models.LuaChonNhaDauTu;

public class ContentQuyetDinhPheDuyet
{
    public Guid? Id { get; set; } // Nullable Guid
    public string? Code { get; set; } // Nullable string
    public string? Version { get; set; } // Nullable string
    public string? Type { get; set; } // Nullable string
    public string? TrangThai { get; set; } // Nullable string
    public string? Name { get; set; } // Nullable string
    public DateTime? NgayDangTai { get; set; } // Nullable DateTime
    public string? InputUserCd { get; set; } // Nullable string
    public string? DonViDangTai { get; set; } // Nullable string
}

public class DecisionApproveProjectPPPOfDetailDTO
{
    public string? Rn { get; set; }
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? PublicDt { get; set; }
    public string? ProjectStatus { get; set; }
    public string? OrganizationNm { get; set; }
    public string? ProjectLandResource { get; set; }
    public string? BidName { get; set; }
    public string? TechnologyApplication { get; set; }
    public string? ProjectPriceFeeService { get; set; }
    public string? RegYn { get; set; }
    public string? BidMethodInvest { get; set; }
    public string? BidInterYn { get; set; }
    public string? PublicYn { get; set; }
    public string? InputUserCd { get; set; }
    public string? ProjectName { get; set; }
    public string? InputDtStr { get; set; }
    public string? BizNm { get; set; }
    public string? ProjectTime { get; set; }
    public string? ProjectType { get; set; }
    public string? ProjectTotalInvest { get; set; }
    public string? ProjectTotalInvestType { get; set; }
    public string? ProjectStructureInvest { get; set; }
    public string? ProjectPurpose { get; set; }
    public string? ProjectLocation { get; set; }
    public string? ProjectScale { get; set; }
    public string? BidSuccessMethod { get; set; }
    public string? InvestTime { get; set; }
    public string? FileName { get; set; }
}

public class BodyQuyetDinhPheDuyet
{
    public DecisionApproveProjectPPPOfDetailDTO? DecisionApproveProjectPPPOfDetailDTO { get; set; }
}
