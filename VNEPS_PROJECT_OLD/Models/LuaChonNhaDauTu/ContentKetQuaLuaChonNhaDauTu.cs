namespace VNEPS_PROJECT_OLD.Models.LuaChonNhaDauTu;

public class ContentKetQuaLuaChonNhaDauTu
{
    public string? Id { get; set; }

    public string? Code { get; set; }

    public string? Version { get; set; }

    public string? Type { get; set; }

    public string? HinhThucDuThau { get; set; }

    public string? MaBenMoiThau { get; set; }

    public string? Name { get; set; }

    public DateTime? NgayDangTai { get; set; }

    public DateTime? NgayDongThau { get; set; }

    public string? TenBenMoiThau { get; set; }

    public string? NhaThauTrungThau { get; set; }

    public string? BidSuccOfflineYn { get; set; }

    public string? LoaiThongBao { get; set; }

    public string? InputUserCd { get; set; }

    public string? ContractMethod { get; set; }
}

public class DetailV2KetQuaLuaChonNhaDauTu
{
    public ResultInvestorSelectionV2DTO? ResultInvestorSelectionV2DTO { get; set; }
}

public class ResultInvestorSelectionV2DTO
{
    public string? BidNo { get; set; }

    public string? BidTurnNo { get; set; }

    public string? BidNm { get; set; }

    public string? ProjectName { get; set; }

    public string? InstituFullNm { get; set; }

    public DateTime? PublicDt { get; set; }

    public DateTime? BidOpenDt { get; set; }

    public DateTime? BidEndDt { get; set; }

    public string? RefNo { get; set; }

    public string? BidInstituCd { get; set; }

    public string? BidSuccOfflineYn { get; set; }

    public decimal? EstiPrice { get; set; }

    public string? CdNm { get; set; }

    public string? BizNm { get; set; }

    public string? SuccBidderYn { get; set; }

    public string? SuccBidderRson { get; set; }

    public string? IneligibleYn { get; set; }

    public string? IneligibleRson { get; set; }

    public decimal? BidPriceSucc { get; set; }

    public DateTime? PublicDtSucc { get; set; }

    public string? PublicYn { get; set; }

    public string? ContractTime { get; set; }

    public string? RefNo1 { get; set; }

    public string? BidType { get; set; }

    public string? ContractMethod { get; set; }

    public string? BidOtherPriceSucc { get; set; }

    public string? BidFilename { get; set; }

    public string? BidNdtAttention { get; set; }

    public string? BidNdtContentFocus { get; set; }

    public string? BidNdtContentApprove { get; set; }

    public string? ProjectLocation { get; set; }

    public string? ProjectPurpose { get; set; }

    public string? BidProjectCapital { get; set; }

    public string? ProjectType { get; set; }

    public string? BidCapitalCurrency { get; set; }

    public string? BidEstimatedCurrency { get; set; }

    public string? BidNdtPppPrice { get; set; }

    public string? BidNdtPppTime { get; set; }

    public string? BidContractTime { get; set; }
}

public class ResultInvestorSelectionV3DTO
{
    public string BidFilename { get; set; }
    public string IneligibleRson { get; set; } // Nullable string for null values
    public string RefNo { get; set; } // Nullable string for null values
}

public class DetailV3KetQuaLuaChonNhaDauTu
{
    public ResultInvestorSelectionV3DTO ResultInvestorSelectionV3DTO { get; set; }
}