namespace VNEPS_PROJECT_OLD.Models.LuatDauThau.DanhSachNgan;

public class DanhSachNganSearch
{
    public string? Id { get; set; }
    public string? Code { get; set; }
    public string? Version { get; set; }

    public string? LinhVuc { get; set; }
    public string? MaKH { get; set; }
    public string? Type { get; set; }
    public string? MaBenMoiThau { get; set; }
    public string? Name { get; set; }
    public string? NgayDangTai { get; set; }
    public string? TenBenMoiThau { get; set; }
    public string? InputUserCd { get; set; }
    public string? NoBidYn { get; set; }
    public string? SuccBidMethod { get; set; }
}

public class DanhSachNganDetail
{
    public BidShortListDetailDTO? BidShortListDetailDTO { get; set; }
    public List<BidderDTOList>? BidderDTOList { get; set; }
}

public class BidShortListDetailDTO
{
    public string? BidType { get; set; }
    public string? KhBidNo { get; set; }
    public string? BidPlanItemNm { get; set; }
    public string? BidPlanProjectNm { get; set; }
    public string? RegYn { get; set; }
    public string? InstituFullNm { get; set; }
    public string? RefNoBidder { get; set; }
    public string? BidPlanItemIsInternational { get; set; }
    public string? BidPlanItemIspq { get; set; }
    public string? BidPlanItemDetailsId { get; set; }
    public string? GtNo { get; set; }
    public string? BidPlanItemMethodSelect { get; set; }
    public string? BidPlanMasterId { get; set; }
    public string? OpenDt { get; set; }
    public string? CancelYn { get; set; }
    public string? CancelReason { get; set; }
    public string? Note { get; set; }
    public string? FileServerName { get; set; }
    public string? FileName { get; set; }
    public string? FileServerQdpd { get; set; }
    public string? FileNameQdpd { get; set; }
}

public class BidderDTOList
{
    public string? BidNo { get; set; }
    public string? BidTurnNo { get; set; }
    public string? BizRegNo { get; set; }
    public string? BizNm { get; set; }
    public string? BidAddress { get; set; }
    public string? BidLocation { get; set; }
    public string? PartnerCode { get; set; }
    public string? PartnerName { get; set; }
}